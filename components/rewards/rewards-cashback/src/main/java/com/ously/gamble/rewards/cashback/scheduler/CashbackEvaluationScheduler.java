package com.ously.gamble.rewards.cashback.scheduler;

import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.rewards.cashback.api.CashbackService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnOffloader
@ConditionalOnProperty(prefix = "rewards.cashback", name = "enabled", havingValue = "true")
public class CashbackEvaluationScheduler {

    private static final Logger log = LoggerFactory.getLogger(CashbackEvaluationScheduler.class);
    private final CashbackService cbService;
    private final ScheduleExecutionService schedSrv;


    public CashbackEvaluationScheduler(CashbackService cbService, ScheduleExecutionService schedSrv) {
        this.cbService = cbService;
        this.schedSrv = schedSrv;
    }

    @Scheduled(cron = "0 */5 * ? * *")
    @SchedulerLock(name = "REWARDS-CASHBACK-evalActive", lockAtLeastFor = "PT2M")
    public void evaluateActiveCashbacks() {
        schedSrv.doSchedule(new ScheduledTaskInformation("cashback", "deleteOldFinished", null), this::deleteOldFinished);
        schedSrv.doSchedule(new ScheduledTaskInformation("cashback", "evaluateActive", null), this::evaluateAllActive);
    }

    private ScheduledTaskInformation evaluateAllActive(ScheduledTaskInformation sti) {
        try {
            cbService.scheduleEvaluationOfActiveCashbacks(10);
        } catch (Exception e) {
            log.error("Error scheduling evaluate cashbacks", e);
        }
        return sti;
    }

    private ScheduledTaskInformation deleteOldFinished(ScheduledTaskInformation sti) {
        try {
            cbService.deleteOldFinishedCashbacks();
        } catch (Exception e) {
            log.error("Error scheduling delete finished cashbacks", e);
        }
        return sti;
    }


}
