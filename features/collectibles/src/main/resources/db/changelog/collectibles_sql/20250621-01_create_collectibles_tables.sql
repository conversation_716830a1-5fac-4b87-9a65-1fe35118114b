-- Drop old tables
DROP TABLE IF EXISTS `collectible`;
DROP TABLE IF EXISTS `collectible_set`;
DROP TABLE IF EXISTS `collectibles_user`;

CREATE TABLE card_collections
(
    id          INT PRIMARY KEY AUTO_INCREMENT,
    name        VARCHAR(100) NOT NULL,
    description TEXT,
    start_date  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date    DATETIME NULL,
    status      ENUM('ENABLED', 'EXPIRED', 'DISABLED') NOT NULL DEFAULT 'DISABLED',
    reward_id   INT NULL,
    sort_order  INT       DEFAULT 99,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX       idx_status_dates (status, start_date, end_date),
    FOREIGN KEY (reward_id) REFERENCES collection_rewards (id) ON DELETE SET NULL
);

CREATE TABLE cards
(
    id                 INT PRIMARY KEY AUTO_INCREMENT,
    card_collection_id INT          NOT NULL,
    name               VARCHAR(100) NOT NULL,
    description        TEXT,
    start_date         DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date           DATETIME NULL,
    status             ENUM('ENABLED', 'EXPIRED', 'DISABLED') NOT NULL DEFAULT 'DISABLED',
    image_url          VARCHAR(255) NOT NULL,
    rarity_level       TINYINT      NOT NULL,
    reward_id          INT NULL,
    sort_order         TINYINT   DEFAULT 99,
    created_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (card_collection_id) REFERENCES card_collections (id) ON DELETE CASCADE,
    FOREIGN KEY (reward_id) REFERENCES collection_rewards (id) ON DELETE SET NULL,
    INDEX              idx_collection_status_dates (card_collection_id, status, start_date, end_date)
);

CREATE TABLE user_cards_pieces
(
    user_id     BIGINT PRIMARY KEY,
    pieces_data JSON NOT NULL,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

TODO вот тут херня, 1 к 1 ревард идет
CREATE TABLE collection_rewards
(
    id                   INT PRIMARY KEY AUTO_INCREMENT,
    reward_type          ENUM('COMPLETION', 'MILESTONE') NOT NULL,
    milestone_percentage TINYINT NULL,          -- for milestone rewards (25, 50, 75)
    reward_data          JSON         NOT NULL,
    name                 VARCHAR(100) NOT NULL,
    description          TEXT,
    created_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX                idx_reward_type (reward_type),
    CHECK (
        (reward_type = 'COMPLETION' AND milestone_percentage IS NULL) OR
        (reward_type = 'MILESTONE' AND milestone_percentage IS NOT NULL)
        )
);

CREATE TABLE user_reward_claims
(
    id                   BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id              BIGINT NOT NULL,
    collection_reward_id INT    NOT NULL,
    card_collection_id   INT    NOT NULL, -- ссылка на коллекцию
    card_id              INT NULL,        -- ссылка на карту (nullable)
    claimed_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at           TIMESTAMP NULL,
    reward_data          JSON   NOT NULL, -- копия данных награды на момент получения

    UNIQUE KEY uk_user_reward (user_id, collection_reward_id),
    INDEX                idx_user_claims (user_id, claimed_at),
    INDEX                idx_reward_claims (collection_reward_id),
    INDEX                idx_collection_claims (card_collection_id),
    INDEX                idx_card_claims (card_id),

    FOREIGN KEY (collection_reward_id) REFERENCES collection_rewards (id),
    FOREIGN KEY (card_collection_id) REFERENCES card_collections (id),
    FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE SET NULL
);

CREATE TABLE piece_drop_log
(
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id         BIGINT NOT NULL,
    puzzle_piece_id INT    NOT NULL,
    drop_source     ENUM('LOOTBOX', 'GAME_PROGRESS', 'EVENT', 'PURCHASE', 'EXCHANGED') NOT NULL,
    drop_context    VARCHAR(100), -- дополнительная информация об источнике
    context_data    JSON,         -- данные контекста на момент дропа
    dropped_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX           idx_user_drops (user_id, dropped_at),
    INDEX           idx_piece_drops (puzzle_piece_id, dropped_at),
    INDEX           idx_source_analysis (drop_source, dropped_at),

    FOREIGN KEY (puzzle_piece_id) REFERENCES puzzle_pieces (id)
);