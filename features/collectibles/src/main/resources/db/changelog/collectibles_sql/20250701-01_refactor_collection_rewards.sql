-- Рефакторинг таблицы collection_rewards для поддержки связи один ко многим
-- с коллекциями и картами

-- Сначала удаляем внешние ключи из существующих таблиц
ALTER TABLE card_collections DROP FOREIGN KEY card_collections_ibfk_1;
ALTER TABLE cards DROP FOREIGN KEY cards_ibfk_2;

-- Удаляем колонки reward_id из таблиц коллекций и карт
ALTER TABLE card_collections DROP COLUMN reward_id;
ALTER TABLE cards DROP COLUMN reward_id;

-- Пересоздаем таблицу collection_rewards с новой структурой
DROP TABLE IF EXISTS collection_rewards;

CREATE TABLE collection_rewards
(
    id                   INT PRIMARY KEY AUTO_INCREMENT,
    card_collection_id   INT NULL,                                              -- связь с коллекцией (NULL если награда для карты)
    card_id              INT NULL,                                              -- связь с картой (NULL если награда для коллекции)
    reward_type          ENUM('COMPLETION', 'MILESTONE') NOT NULL,
    milestone_percentage TINYINT NULL,                                          -- процент для milestone наград (1-100)
    reward_data          JSON         NOT NULL,
    created_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Ограничения: награда должна быть либо для коллекции, либо для карты, но не для обеих
    CHECK (
        (card_collection_id IS NOT NULL AND card_id IS NULL) OR
        (card_collection_id IS NULL AND card_id IS NOT NULL)
    ),
    
    -- Ограничения для типов наград
    CHECK (
        (reward_type = 'COMPLETION' AND milestone_percentage IS NULL) OR
        (reward_type = 'MILESTONE' AND milestone_percentage IS NOT NULL AND milestone_percentage BETWEEN 1 AND 100)
    ),

    -- Индексы для быстрого поиска наград по коллекциям и картам
    INDEX idx_collection_rewards (card_collection_id, reward_type),
    INDEX idx_card_rewards (card_id, reward_type),
    INDEX idx_milestone_rewards (reward_type, milestone_percentage),

    -- Внешние ключи
    FOREIGN KEY (card_collection_id) REFERENCES card_collections (id) ON DELETE CASCADE,
    FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE
);

-- Обновляем таблицу user_reward_claims для корректной работы с новой структурой
-- Удаляем старый уникальный ключ
ALTER TABLE user_reward_claims DROP INDEX uk_user_reward;

-- Добавляем новый составной уникальный ключ, который учитывает что пользователь может получить
-- одну и ту же награду только один раз для конкретной коллекции или карты
ALTER TABLE user_reward_claims 
ADD CONSTRAINT uk_user_collection_card_reward 
UNIQUE (user_id, collection_reward_id, card_collection_id, card_id);
