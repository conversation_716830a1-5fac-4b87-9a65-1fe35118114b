package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CardCollectionMapper {

    public CardCollectionDto.CardCollectionResponse toResponse(CardCollection collection) {
        return new CardCollectionDto.CardCollectionResponse(
                collection.getId(),
                collection.getName(),
                collection.getDescription(),
                collection.getStartDate(),
                collection.getEndDate(),
                collection.getStatus(),
                collection.getSortOrder(),
                collection.getCards().stream()
                        .map(this::toCardResponse)
                        .collect(Collectors.toList()),
                collection.getRewards().stream()
                        .filter(reward -> reward.isForCollection())
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardCollectionDto.CardCollectionSummaryResponse toSummaryResponse(CardCollection collection) {
        return new CardCollectionDto.CardCollectionSummaryResponse(
                collection.getId(),
                collection.getName(),
                collection.getDescription(),
                collection.getStatus(),
                collection.getStartDate(),
                collection.getEndDate(),
                collection.getCards().size(),
                collection.getRewards().size()
        );
    }

    public CardCollectionDto.CardResponse toCardResponse(Card card) {
        return new CardCollectionDto.CardResponse(
                card.getId(),
                card.getName(),
                card.getImageUrl(),
                card.getRarityLevel(),
                card.getStartDate(),
                card.getEndDate(),
                card.getStatus(),
                card.getSortOrder(),
                card.getRewards().stream()
                        .map(this::toRewardResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardCollectionDto.RewardResponse toRewardResponse(Reward reward) {
        String targetType = reward.isForCollection() ? "COLLECTION" : "CARD";
        Integer targetId = reward.getTargetId();
        String targetName = reward.getTargetName();

        return new CardCollectionDto.RewardResponse(
                reward.getId(),
                mapRewardType(reward.getRewardType()),
                reward.getMilestonePercentage(),
                reward.getRewardData(),
                targetType,
                targetId,
                targetName
        );
    }

    public List<CardCollectionDto.CardCollectionSummaryResponse> toSummaryResponseList(List<CardCollection> collections) {
        return collections.stream()
                .map(this::toSummaryResponse)
                .collect(Collectors.toList());
    }

    public List<CardCollectionDto.CardResponse> toCardResponseList(List<Card> cards) {
        return cards.stream()
                .map(this::toCardResponse)
                .collect(Collectors.toList());
    }

    private CardCollectionDto.RewardResponse.RewardType mapRewardType(Reward.RewardType rewardType) {
        return switch (rewardType) {
            case COMPLETION -> CardCollectionDto.RewardResponse.RewardType.COMPLETION;
            case MILESTONE -> CardCollectionDto.RewardResponse.RewardType.MILESTONE;
        };
    }

    public Reward.RewardType mapRewardType(CardCollectionDto.CreateRewardRequest.RewardType rewardType) {
        return switch (rewardType) {
            case COMPLETION -> Reward.RewardType.COMPLETION;
            case MILESTONE -> Reward.RewardType.MILESTONE;
        };
    }
}
