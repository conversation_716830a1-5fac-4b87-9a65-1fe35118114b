//package com.ously.gamble.collectibles.controller;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.ously.gamble.collectibles.persistence.model.Reward;
//import com.ously.gamble.collectibles.service.RewardService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.security.SecurityRequirement;
//import jakarta.annotation.security.RolesAllowed;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.web.PageableDefault;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/api/admin/rewards")
//public class RewardController {
//
//    private final RewardService rewardService;
//
//    public RewardController(RewardService rewardService) {
//        this.rewardService = rewardService;
//    }
//
//    @Operation(description = "Get all rewards", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping
//    @RolesAllowed("ADMIN")
//    public List<Reward> getAllRewards() {
//        return rewardService.findAll();
//    }
//
//    @Operation(description = "Get rewards with pagination", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/paged")
//    @RolesAllowed("ADMIN")
//    public Page<Reward> getRewardsPaged(@PageableDefault(size = 20) Pageable pageable) {
//        return rewardService.findAll(pageable);
//    }
//
//    @Operation(description = "Get rewards by collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/collection/{collectionId}")
//    @RolesAllowed("ADMIN")
//    public List<Reward> getRewardsByCollection(@PathVariable Integer collectionId) {
//        return rewardService.findByCollectionId(collectionId);
//    }
//
//    @Operation(description = "Get rewards by card", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/card/{cardId}")
//    @RolesAllowed("ADMIN")
//    public List<Reward> getRewardsByCard(@PathVariable Integer cardId) {
//        return rewardService.findByCardId(cardId);
//    }
//
//    @Operation(description = "Get completion rewards", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/completion")
//    @RolesAllowed("ADMIN")
//    public List<Reward> getCompletionRewards() {
//        return rewardService.findCompletionRewards();
//    }
//
//    @Operation(description = "Get milestone rewards", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/milestone")
//    @RolesAllowed("ADMIN")
//    public List<Reward> getMilestoneRewards() {
//        return rewardService.findMilestoneRewards();
//    }
//
//    @Operation(description = "Get milestone rewards by percentage", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/milestone/{percentage}")
//    @RolesAllowed("ADMIN")
//    public List<Reward> getMilestoneRewardsByPercentage(@PathVariable Byte percentage) {
//        return rewardService.findMilestoneRewardsByPercentage(percentage);
//    }
//
//    @Operation(description = "Get reward by ID", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Reward> getRewardById(@PathVariable Integer id) {
//        return rewardService.findById(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Create new reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping
//    @RolesAllowed("ADMIN")
//    public Reward createReward(@RequestBody CreateRewardRequest request) {
//        return rewardService.create(request);
//    }
//
//    @Operation(description = "Update reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @PutMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Reward> updateReward(
//            @PathVariable Integer id,
//            @RequestBody UpdateRewardRequest request) {
//        return rewardService.update(id, request)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Delete reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Void> deleteReward(@PathVariable Integer id) {
//        if (rewardService.delete(id)) {
//            return ResponseEntity.noContent().build();
//        }
//        return ResponseEntity.notFound().build();
//    }
//
//    // DTOs
//    public record CreateRewardRequest(
//            Integer cardCollectionId,
//            Integer cardId,
//            Reward.RewardType rewardType,
//            Byte milestonePercentage,
//            JsonNode rewardData
//    ) {}
//
//    public record UpdateRewardRequest(
//            Reward.RewardType rewardType,
//            Byte milestonePercentage,
//            JsonNode rewardData
//    ) {}
//}
