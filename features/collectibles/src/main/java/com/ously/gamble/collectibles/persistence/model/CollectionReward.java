package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "collection_rewards")
public class CollectionReward {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_collection_id")
    private CardCollection cardCollection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_id")
    private Card card;

    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type", nullable = false)
    private RewardType rewardType;

    @Column(name = "milestone_percentage")
    private Byte milestonePercentage;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "reward_data", nullable = false, columnDefinition = "JSON")
    private JsonNode rewardData;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public enum RewardType {
        COMPLETION, MILESTONE
    }

    // Rich Domain Model methods
    public boolean isForCollection() {
        return cardCollection != null && card == null;
    }

    public boolean isForCard() {
        return card != null && cardCollection == null;
    }

    public boolean isCompletionReward() {
        return rewardType == RewardType.COMPLETION;
    }

    public boolean isMilestoneReward() {
        return rewardType == RewardType.MILESTONE;
    }

    public void validateReward() {
        if (cardCollection == null && card == null) {
            throw new IllegalStateException("Reward must be associated with either collection or card");
        }
        if (cardCollection != null && card != null) {
            throw new IllegalStateException("Reward cannot be associated with both collection and card");
        }
        if (rewardType == RewardType.MILESTONE && milestonePercentage == null) {
            throw new IllegalStateException("Milestone reward must have percentage");
        }
        if (rewardType == RewardType.COMPLETION && milestonePercentage != null) {
            throw new IllegalStateException("Completion reward cannot have percentage");
        }
        if (milestonePercentage != null && (milestonePercentage < 1 || milestonePercentage > 100)) {
            throw new IllegalStateException("Milestone percentage must be between 1 and 100");
        }
    }

    public String getTargetName() {
        if (isForCollection()) {
            return cardCollection.getName();
        } else if (isForCard()) {
            return card.getName();
        }
        return "Unknown";
    }

    public Integer getTargetId() {
        if (isForCollection()) {
            return cardCollection.getId();
        } else if (isForCard()) {
            return card.getId();
        }
        return null;
    }

    // Constructors
    public CollectionReward() {}

    public CollectionReward(CardCollection cardCollection, RewardType rewardType, JsonNode rewardData) {
        this.cardCollection = cardCollection;
        this.rewardType = rewardType;
        this.rewardData = rewardData;
        validateReward();
    }

    public CollectionReward(Card card, RewardType rewardType, JsonNode rewardData) {
        this.card = card;
        this.rewardType = rewardType;
        this.rewardData = rewardData;
        validateReward();
    }

    public CollectionReward(CardCollection cardCollection, RewardType rewardType, Byte milestonePercentage, JsonNode rewardData) {
        this.cardCollection = cardCollection;
        this.rewardType = rewardType;
        this.milestonePercentage = milestonePercentage;
        this.rewardData = rewardData;
        validateReward();
    }

    public CollectionReward(Card card, RewardType rewardType, Byte milestonePercentage, JsonNode rewardData) {
        this.card = card;
        this.rewardType = rewardType;
        this.milestonePercentage = milestonePercentage;
        this.rewardData = rewardData;
        validateReward();
    }

    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public CardCollection getCardCollection() { return cardCollection; }
    public void setCardCollection(CardCollection cardCollection) { 
        this.cardCollection = cardCollection;
        validateReward();
    }

    public Card getCard() { return card; }
    public void setCard(Card card) { 
        this.card = card;
        validateReward();
    }

    public RewardType getRewardType() { return rewardType; }
    public void setRewardType(RewardType rewardType) { 
        this.rewardType = rewardType;
        validateReward();
    }

    public Byte getMilestonePercentage() { return milestonePercentage; }
    public void setMilestonePercentage(Byte milestonePercentage) { 
        this.milestonePercentage = milestonePercentage;
        validateReward();
    }

    public JsonNode getRewardData() { return rewardData; }
    public void setRewardData(JsonNode rewardData) { this.rewardData = rewardData; }

    public LocalDateTime getCreatedAt() { return createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CollectionReward that = (CollectionReward) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "CollectionReward{" +
                "id=" + id +
                ", rewardType=" + rewardType +
                ", milestonePercentage=" + milestonePercentage +
                ", target=" + getTargetName() +
                '}';
    }
}
