package com.ously.gamble.collectibles.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.PieceDropLog;
import com.ously.gamble.collectibles.service.PieceDropLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/admin/piece-drop-logs")
public class PieceDropLogController {

    private final PieceDropLogService pieceDropLogService;

    public PieceDropLogController(PieceDropLogService pieceDropLogService) {
        this.pieceDropLogService = pieceDropLogService;
    }

    @Operation(description = "Get all piece drop logs", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getAllDropLogs() {
        return pieceDropLogService.findAll();
    }

    @Operation(description = "Get piece drop logs with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<PieceDropLog> getDropLogsPaged(@PageableDefault(size = 20) Pageable pageable) {
        return pieceDropLogService.findAll(pageable);
    }

    @Operation(description = "Get drop logs by user", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsByUser(@PathVariable Long userId) {
        return pieceDropLogService.findByUserId(userId);
    }

    @Operation(description = "Get drop logs by puzzle piece", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/piece/{puzzlePieceId}")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsByPuzzlePiece(@PathVariable Integer puzzlePieceId) {
        return pieceDropLogService.findByPuzzlePieceId(puzzlePieceId);
    }

    @Operation(description = "Get drop logs by source", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/source/{source}")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsBySource(@PathVariable PieceDropLog.DropSource source) {
        return pieceDropLogService.findByDropSource(source);
    }

    @Operation(description = "Get drop logs by user and source", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/source/{source}")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsByUserAndSource(
            @PathVariable Long userId,
            @PathVariable PieceDropLog.DropSource source) {
        return pieceDropLogService.findByUserIdAndDropSource(userId, source);
    }

    @Operation(description = "Get drop logs by date range", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/date-range")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsByDateRange(
            @RequestParam LocalDateTime startDate,
            @RequestParam LocalDateTime endDate) {
        return pieceDropLogService.findByDateRange(startDate, endDate);
    }

    @Operation(description = "Get drop logs by user and date range", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/date-range")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> getDropLogsByUserAndDateRange(
            @PathVariable Long userId,
            @RequestParam LocalDateTime startDate,
            @RequestParam LocalDateTime endDate) {
        return pieceDropLogService.findByUserIdAndDateRange(userId, startDate, endDate);
    }

    @Operation(description = "Get drop statistics by source", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/stats/source")
    @RolesAllowed("ADMIN")
    public List<DropSourceStats> getDropStatsBySource() {
        return pieceDropLogService.getDropStatsBySource();
    }

    @Operation(description = "Get drop statistics by user", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/stats/user/{userId}")
    @RolesAllowed("ADMIN")
    public List<UserDropStats> getDropStatsByUser(@PathVariable Long userId) {
        return pieceDropLogService.getDropStatsByUser(userId);
    }

    @Operation(description = "Get piece drop log by ID", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<PieceDropLog> getDropLogById(@PathVariable Long id) {
        return pieceDropLogService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create new piece drop log", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public PieceDropLog createDropLog(@RequestBody CreatePieceDropLogRequest request) {
        return pieceDropLogService.create(request);
    }

    @Operation(description = "Log multiple piece drops", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/batch")
    @RolesAllowed("ADMIN")
    public List<PieceDropLog> createDropLogsBatch(@RequestBody List<CreatePieceDropLogRequest> requests) {
        return pieceDropLogService.createBatch(requests);
    }

    @Operation(description = "Delete piece drop log", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteDropLog(@PathVariable Long id) {
        if (pieceDropLogService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // DTOs
    public record CreatePieceDropLogRequest(
            Long userId,
            Integer puzzlePieceId,
            PieceDropLog.DropSource dropSource,
            String dropContext,
            JsonNode contextData
    ) {}

    public record DropSourceStats(
            PieceDropLog.DropSource source,
            Long totalDrops,
            Long uniqueUsers,
            LocalDateTime firstDrop,
            LocalDateTime lastDrop
    ) {}

    public record UserDropStats(
            Long userId,
            PieceDropLog.DropSource source,
            Long totalDrops,
            LocalDateTime firstDrop,
            LocalDateTime lastDrop
    ) {}
}
