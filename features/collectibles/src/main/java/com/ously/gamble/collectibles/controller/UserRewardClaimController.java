//package com.ously.gamble.collectibles.controller;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.ously.gamble.collectibles.persistence.model.UserRewardClaim;
//import com.ously.gamble.collectibles.service.UserRewardClaimService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.security.SecurityRequirement;
//import jakarta.annotation.security.RolesAllowed;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.web.PageableDefault;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//@RestController
//@RequestMapping("/api/admin/user-reward-claims")
//public class UserRewardClaimController {
//
//    private final UserRewardClaimService userRewardClaimService;
//
//    public UserRewardClaimController(UserRewardClaimService userRewardClaimService) {
//        this.userRewardClaimService = userRewardClaimService;
//    }
//
//    @Operation(description = "Get all user reward claims", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getAllClaims() {
//        return userRewardClaimService.findAll();
//    }
//
//    @Operation(description = "Get user reward claims with pagination", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/paged")
//    @RolesAllowed("ADMIN")
//    public Page<UserRewardClaim> getClaimsPaged(@PageableDefault(size = 20) Pageable pageable) {
//        return userRewardClaimService.findAll(pageable);
//    }
//
//    @Operation(description = "Get active user reward claims", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/active")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getActiveClaims() {
//        return userRewardClaimService.findActiveClaims();
//    }
//
//    @Operation(description = "Get claims by user", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/user/{userId}")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getClaimsByUser(@PathVariable Long userId) {
//        return userRewardClaimService.findByUserId(userId);
//    }
//
//    @Operation(description = "Get active claims by user", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/user/{userId}/active")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getActiveClaimsByUser(@PathVariable Long userId) {
//        return userRewardClaimService.findActiveClaimsByUserId(userId);
//    }
//
//    @Operation(description = "Get claims by collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/collection/{collectionId}")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getClaimsByCollection(@PathVariable Integer collectionId) {
//        return userRewardClaimService.findByCollectionId(collectionId);
//    }
//
//    @Operation(description = "Get claims by card", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/card/{cardId}")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getClaimsByCard(@PathVariable Integer cardId) {
//        return userRewardClaimService.findByCardId(cardId);
//    }
//
//    @Operation(description = "Get claims by reward", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/reward/{rewardId}")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getClaimsByReward(@PathVariable Integer rewardId) {
//        return userRewardClaimService.findByRewardId(rewardId);
//    }
//
//    @Operation(description = "Get claims by date range", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/date-range")
//    @RolesAllowed("ADMIN")
//    public List<UserRewardClaim> getClaimsByDateRange(
//            @RequestParam LocalDateTime startDate,
//            @RequestParam LocalDateTime endDate) {
//        return userRewardClaimService.findByDateRange(startDate, endDate);
//    }
//
//    @Operation(description = "Get user reward claim by ID", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<UserRewardClaim> getClaimById(@PathVariable Long id) {
//        return userRewardClaimService.findById(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Create new user reward claim", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping
//    @RolesAllowed("ADMIN")
//    public UserRewardClaim createClaim(@RequestBody CreateUserRewardClaimRequest request) {
//        return userRewardClaimService.create(request);
//    }
//
//    @Operation(description = "Mark claim as deleted", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{id}/delete")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<UserRewardClaim> markClaimAsDeleted(@PathVariable Long id) {
//        return userRewardClaimService.markAsDeleted(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Restore deleted claim", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{id}/restore")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<UserRewardClaim> restoreClaim(@PathVariable Long id) {
//        return userRewardClaimService.restore(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Permanently delete claim", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Void> deleteClaim(@PathVariable Long id) {
//        if (userRewardClaimService.delete(id)) {
//            return ResponseEntity.noContent().build();
//        }
//        return ResponseEntity.notFound().build();
//    }
//
//    // DTOs
//    public record CreateUserRewardClaimRequest(
//            Long userId,
//            Integer collectionRewardId,
//            Integer cardCollectionId,
//            Integer cardId,
//            JsonNode rewardData
//    ) {}
//}
