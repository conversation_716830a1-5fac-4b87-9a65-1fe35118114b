package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.CardController.CreateCardRequest;
import com.ously.gamble.collectibles.controller.CardController.UpdateCardRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CardService {

    private final CardRepository cardRepository;
    private final CardCollectionRepository cardCollectionRepository;

    public CardService(CardRepository cardRepository, CardCollectionRepository cardCollectionRepository) {
        this.cardRepository = cardRepository;
        this.cardCollectionRepository = cardCollectionRepository;
    }

    @Transactional(readOnly = true)
    public List<Card> findAll() {
        return cardRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<Card> findAll(Pageable pageable) {
        return cardRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<Card> findById(Integer id) {
        return cardRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<Card> findByIdWithRewards(Integer id) {
        return Optional.ofNullable(cardRepository.findByIdWithRewards(id));
    }

    @Transactional(readOnly = true)
    public Optional<Card> findByIdWithCollectionAndRewards(Integer id) {
        return Optional.ofNullable(cardRepository.findByIdWithCollectionAndRewards(id));
    }

    @Transactional(readOnly = true)
    public List<Card> findByCollectionId(Integer collectionId) {
        return cardRepository.findByCardCollectionIdOrderBySortOrderAscCreatedAtAsc(collectionId);
    }

    @Transactional(readOnly = true)
    public List<Card> findActiveCards() {
        return cardRepository.findActiveCards(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<Card> findActiveCardsByCollection(Integer collectionId) {
        return cardRepository.findActiveCardsByCollection(collectionId, LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<Card> findExpiredCards() {
        return cardRepository.findExpiredCards(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<Card> findByStatus(Card.CardStatus status) {
        return cardRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<Card> findByRarityLevel(Byte rarityLevel) {
        return cardRepository.findByRarityLevel(rarityLevel);
    }

    @Transactional(readOnly = true)
    public List<Card> findRareCards() {
        return cardRepository.findRareCards();
    }

    @Transactional(readOnly = true)
    public List<Card> findCommonCards() {
        return cardRepository.findCommonCards();
    }

    @Transactional(readOnly = true)
    public List<Card> findUncommonCards() {
        return cardRepository.findUncommonCards();
    }

    @Transactional(readOnly = true)
    public List<Card> findByName(String name) {
        return cardRepository.findByNameContainingIgnoreCase(name);
    }

    @Transactional(readOnly = true)
    public List<Card> findExpiringBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return cardRepository.findExpiringBetween(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public long countByCollectionId(Integer collectionId) {
        return cardRepository.countByCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public long countByCollectionIdAndStatus(Integer collectionId, Card.CardStatus status) {
        return cardRepository.countByCollectionIdAndStatus(collectionId, status);
    }

    @Transactional(readOnly = true)
    public long countByRarityLevel(Byte rarityLevel) {
        return cardRepository.countByRarityLevel(rarityLevel);
    }

    public Card create(CreateCardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(request.cardCollectionId())
                .orElseThrow(() -> new IllegalArgumentException("Card collection not found: " + request.cardCollectionId()));

        Card card = new Card(request.name(), collection, request.imageUrl(), request.rarityLevel());
        
        if (request.description() != null) {
            card.setDescription(request.description());
        }
        if (request.startDate() != null) {
            card.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            card.setEndDate(request.endDate());
        }
        if (request.sortOrder() != null) {
            card.setSortOrder(request.sortOrder());
        }
        
        return cardRepository.save(card);
    }

    public Optional<Card> update(Integer id, UpdateCardRequest request) {
        return cardRepository.findById(id)
                .map(card -> {
                    if (request.name() != null) {
                        card.setName(request.name());
                    }
                    if (request.description() != null) {
                        card.setDescription(request.description());
                    }
                    if (request.startDate() != null) {
                        card.setStartDate(request.startDate());
                    }
                    if (request.endDate() != null) {
                        card.setEndDate(request.endDate());
                    }
                    if (request.status() != null) {
                        card.setStatus(request.status());
                    }
                    if (request.imageUrl() != null) {
                        card.setImageUrl(request.imageUrl());
                    }
                    if (request.rarityLevel() != null) {
                        card.setRarityLevel(request.rarityLevel());
                    }
                    if (request.sortOrder() != null) {
                        card.setSortOrder(request.sortOrder());
                    }
                    return cardRepository.save(card);
                });
    }

    public boolean delete(Integer id) {
        if (cardRepository.existsById(id)) {
            cardRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Optional<Card> activate(Integer id) {
        return cardRepository.findById(id)
                .map(card -> {
                    card.activate();
                    return cardRepository.save(card);
                });
    }

    public Optional<Card> disable(Integer id) {
        return cardRepository.findById(id)
                .map(card -> {
                    card.disable();
                    return cardRepository.save(card);
                });
    }

    public Optional<Card> expire(Integer id) {
        return cardRepository.findById(id)
                .map(card -> {
                    card.expire();
                    return cardRepository.save(card);
                });
    }

    public void expireCardsEndingBefore(LocalDateTime dateTime) {
        List<Card> expiring = cardRepository.findExpiringBetween(
                LocalDateTime.now().minusYears(10), dateTime);
        
        expiring.forEach(card -> {
            if (!card.isExpired()) {
                card.expire();
            }
        });
        
        cardRepository.saveAll(expiring);
    }

    @Transactional(readOnly = true)
    public boolean isCardActive(Integer id) {
        return cardRepository.findById(id)
                .map(Card::isActive)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isCardExpired(Integer id) {
        return cardRepository.findById(id)
                .map(Card::isExpired)
                .orElse(true);
    }
}
