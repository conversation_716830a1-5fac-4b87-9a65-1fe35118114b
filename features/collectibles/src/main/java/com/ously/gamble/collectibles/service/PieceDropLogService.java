//package com.ously.gamble.collectibles.service;
//
//import com.ously.gamble.collectibles.controller.PieceDropLogController.CreatePieceDropLogRequest;
//import com.ously.gamble.collectibles.controller.PieceDropLogController.DropSourceStats;
//import com.ously.gamble.collectibles.controller.PieceDropLogController.UserDropStats;
//import com.ously.gamble.collectibles.persistence.model.PieceDropLog;
//import com.ously.gamble.collectibles.persistence.repository.PieceDropLogRepository;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@Service
//@Transactional
//public class PieceDropLogService {
//
//    private final PieceDropLogRepository pieceDropLogRepository;
//
//    public PieceDropLogService(PieceDropLogRepository pieceDropLogRepository) {
//        this.pieceDropLogRepository = pieceDropLogRepository;
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findAll() {
//        return pieceDropLogRepository.findAll();
//    }
//
//    @Transactional(readOnly = true)
//    public Page<PieceDropLog> findAll(Pageable pageable) {
//        return pieceDropLogRepository.findAll(pageable);
//    }
//
//    @Transactional(readOnly = true)
//    public Optional<PieceDropLog> findById(Long id) {
//        return pieceDropLogRepository.findById(id);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByUserId(Long userId) {
//        return pieceDropLogRepository.findByUserIdOrderByDroppedAtDesc(userId);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByPuzzlePieceId(Integer puzzlePieceId) {
//        return pieceDropLogRepository.findByPuzzlePieceIdOrderByDroppedAtDesc(puzzlePieceId);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByDropSource(PieceDropLog.DropSource dropSource) {
//        return pieceDropLogRepository.findByDropSourceOrderByDroppedAtDesc(dropSource);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByUserIdAndDropSource(Long userId, PieceDropLog.DropSource dropSource) {
//        return pieceDropLogRepository.findByUserIdAndDropSource(userId, dropSource);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
//        return pieceDropLogRepository.findByDroppedAtBetween(startDate, endDate);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByUserIdAndDateRange(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
//        return pieceDropLogRepository.findByUserIdAndDroppedAtBetween(userId, startDate, endDate);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findRecentDrops(LocalDateTime since) {
//        return pieceDropLogRepository.findRecentDrops(since);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findRecentDropsByUser(Long userId, LocalDateTime since) {
//        return pieceDropLogRepository.findRecentDropsByUser(userId, since);
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findWithContext() {
//        return pieceDropLogRepository.findWithContext();
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findWithContextData() {
//        return pieceDropLogRepository.findWithContextData();
//    }
//
//    @Transactional(readOnly = true)
//    public List<PieceDropLog> findByDropContext(String context) {
//        return pieceDropLogRepository.findByDropContextContaining(context);
//    }
//
//    @Transactional(readOnly = true)
//    public long countByUserId(Long userId) {
//        return pieceDropLogRepository.countByUserId(userId);
//    }
//
//    @Transactional(readOnly = true)
//    public long countByPuzzlePieceId(Integer puzzlePieceId) {
//        return pieceDropLogRepository.countByPuzzlePieceId(puzzlePieceId);
//    }
//
//    @Transactional(readOnly = true)
//    public long countByDropSource(PieceDropLog.DropSource dropSource) {
//        return pieceDropLogRepository.countByDropSource(dropSource);
//    }
//
//    @Transactional(readOnly = true)
//    public long countByUserIdAndDropSource(Long userId, PieceDropLog.DropSource dropSource) {
//        return pieceDropLogRepository.countByUserIdAndDropSource(userId, dropSource);
//    }
//
//    @Transactional(readOnly = true)
//    public long countDistinctUsersByDropSource(PieceDropLog.DropSource dropSource) {
//        return pieceDropLogRepository.countDistinctUsersByDropSource(dropSource);
//    }
//
//    @Transactional(readOnly = true)
//    public long countDistinctPuzzlePiecesByUserId(Long userId) {
//        return pieceDropLogRepository.countDistinctPuzzlePiecesByUserId(userId);
//    }
//
//    @Transactional(readOnly = true)
//    public List<DropSourceStats> getDropStatsBySource() {
//        List<Object[]> results = pieceDropLogRepository.getDropStatsBySource();
//        return results.stream()
//                .map(row -> new DropSourceStats(
//                        (PieceDropLog.DropSource) row[0],
//                        (Long) row[1],
//                        (Long) row[2],
//                        (LocalDateTime) row[3],
//                        (LocalDateTime) row[4]
//                ))
//                .collect(Collectors.toList());
//    }
//
//    @Transactional(readOnly = true)
//    public List<UserDropStats> getDropStatsByUser(Long userId) {
//        List<Object[]> results = pieceDropLogRepository.getDropStatsByUser(userId);
//        return results.stream()
//                .map(row -> new UserDropStats(
//                        userId,
//                        (PieceDropLog.DropSource) row[0],
//                        (Long) row[1],
//                        (LocalDateTime) row[2],
//                        (LocalDateTime) row[3]
//                ))
//                .collect(Collectors.toList());
//    }
//
//    public PieceDropLog create(CreatePieceDropLogRequest request) {
//        validateCreateRequest(request);
//
//        PieceDropLog dropLog;
//        if (request.contextData() != null) {
//            dropLog = PieceDropLog.logDropWithFullContext(
//                request.userId(), request.puzzlePieceId(), request.dropSource(),
//                request.dropContext(), request.contextData());
//        } else if (request.dropContext() != null) {
//            dropLog = PieceDropLog.logDropWithContext(
//                request.userId(), request.puzzlePieceId(), request.dropSource(), request.dropContext());
//        } else {
//            dropLog = PieceDropLog.logDrop(
//                request.userId(), request.puzzlePieceId(), request.dropSource());
//        }
//
//        return pieceDropLogRepository.save(dropLog);
//    }
//
//    public List<PieceDropLog> createBatch(List<CreatePieceDropLogRequest> requests) {
//        List<PieceDropLog> dropLogs = requests.stream()
//                .map(request -> {
//                    validateCreateRequest(request);
//                    if (request.contextData() != null) {
//                        return PieceDropLog.logDropWithFullContext(
//                            request.userId(), request.puzzlePieceId(), request.dropSource(),
//                            request.dropContext(), request.contextData());
//                    } else if (request.dropContext() != null) {
//                        return PieceDropLog.logDropWithContext(
//                            request.userId(), request.puzzlePieceId(), request.dropSource(), request.dropContext());
//                    } else {
//                        return PieceDropLog.logDrop(
//                            request.userId(), request.puzzlePieceId(), request.dropSource());
//                    }
//                })
//                .collect(Collectors.toList());
//
//        return pieceDropLogRepository.saveAll(dropLogs);
//    }
//
//    public boolean delete(Long id) {
//        if (pieceDropLogRepository.existsById(id)) {
//            pieceDropLogRepository.deleteById(id);
//            return true;
//        }
//        return false;
//    }
//
//    public PieceDropLog logDrop(Long userId, Integer puzzlePieceId, PieceDropLog.DropSource dropSource) {
//        PieceDropLog dropLog = PieceDropLog.logDrop(userId, puzzlePieceId, dropSource);
//        return pieceDropLogRepository.save(dropLog);
//    }
//
//    public PieceDropLog logDropWithContext(Long userId, Integer puzzlePieceId,
//                                         PieceDropLog.DropSource dropSource, String context) {
//        PieceDropLog dropLog = PieceDropLog.logDropWithContext(userId, puzzlePieceId, dropSource, context);
//        return pieceDropLogRepository.save(dropLog);
//    }
//
//    private void validateCreateRequest(CreatePieceDropLogRequest request) {
//        if (request.userId() == null) {
//            throw new IllegalArgumentException("User ID is required");
//        }
//        if (request.puzzlePieceId() == null) {
//            throw new IllegalArgumentException("Puzzle piece ID is required");
//        }
//        if (request.dropSource() == null) {
//            throw new IllegalArgumentException("Drop source is required");
//        }
//    }
//
//    @Transactional(readOnly = true)
//    public boolean hasUserDroppedPiece(Long userId, Integer puzzlePieceId) {
//        return !pieceDropLogRepository.findByUserIdAndPuzzlePieceId(userId, puzzlePieceId).isEmpty();
//    }
//
//    @Transactional(readOnly = true)
//    public boolean hasUserDroppedFromSource(Long userId, PieceDropLog.DropSource dropSource) {
//        return !pieceDropLogRepository.findByUserIdAndDropSource(userId, dropSource).isEmpty();
//    }
//}
