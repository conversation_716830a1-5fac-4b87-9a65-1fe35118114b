package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.service.CardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/cards")
public class CardController {

    private final CardService cardService;

    public CardController(CardService cardService) {
        this.cardService = cardService;
    }

    @Operation(description = "Get all cards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<Card> getAllCards() {
        return cardService.findAll();
    }

    @Operation(description = "Get cards with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<Card> getCardsPaged(@PageableDefault(size = 20) Pageable pageable) {
        return cardService.findAll(pageable);
    }

    @Operation(description = "Get cards by collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collection/{collectionId}")
    @RolesAllowed("ADMIN")
    public List<Card> getCardsByCollection(@PathVariable Integer collectionId) {
        return cardService.findByCollectionId(collectionId);
    }

    @Operation(description = "Get active cards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/active")
    @RolesAllowed("ADMIN")
    public List<Card> getActiveCards() {
        return cardService.findActiveCards();
    }

    @Operation(description = "Get cards by rarity level", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/rarity/{level}")
    @RolesAllowed("ADMIN")
    public List<Card> getCardsByRarity(@PathVariable Byte level) {
        return cardService.findByRarityLevel(level);
    }

    @Operation(description = "Get card by ID", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Card> getCardById(@PathVariable Integer id) {
        return cardService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create new card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public Card createCard(@RequestBody CreateCardRequest request) {
        return cardService.create(request);
    }

    @Operation(description = "Update card", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Card> updateCard(
            @PathVariable Integer id,
            @RequestBody UpdateCardRequest request) {
        return cardService.update(id, request)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Delete card", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCard(@PathVariable Integer id) {
        if (cardService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(description = "Activate card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/activate")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Card> activateCard(@PathVariable Integer id) {
        return cardService.activate(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Disable card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/disable")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Card> disableCard(@PathVariable Integer id) {
        return cardService.disable(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Expire card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/expire")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Card> expireCard(@PathVariable Integer id) {
        return cardService.expire(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // DTOs
    public record CreateCardRequest(
            Integer cardCollectionId,
            String name,
            String description,
            java.time.LocalDateTime startDate,
            java.time.LocalDateTime endDate,
            String imageUrl,
            Byte rarityLevel,
            Byte sortOrder
    ) {}

    public record UpdateCardRequest(
            String name,
            String description,
            java.time.LocalDateTime startDate,
            java.time.LocalDateTime endDate,
            Card.CardStatus status,
            String imageUrl,
            Byte rarityLevel,
            Byte sortOrder
    ) {}
}
