//package com.ously.gamble.collectibles.persistence.repository;
//
//import com.ously.gamble.collectibles.persistence.model.PieceDropLog;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//import org.springframework.stereotype.Repository;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//@Repository
//public interface PieceDropLogRepository extends JpaRepository<PieceDropLog, Long> {
//
//    List<PieceDropLog> findByUserId(Long userId);
//
//    List<PieceDropLog> findByPuzzlePieceId(Integer puzzlePieceId);
//
//    List<PieceDropLog> findByDropSource(PieceDropLog.DropSource dropSource);
//
//    List<PieceDropLog> findByUserIdAndDropSource(Long userId, PieceDropLog.DropSource dropSource);
//
//    List<PieceDropLog> findByUserIdAndPuzzlePieceId(Long userId, Integer puzzlePieceId);
//
//    List<PieceDropLog> findByDroppedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
//
//    List<PieceDropLog> findByUserIdAndDroppedAtBetween(Long userId, LocalDateTime startDate, LocalDateTime endDate);
//
//    List<PieceDropLog> findByDropSourceAndDroppedAtBetween(PieceDropLog.DropSource dropSource,
//                                                         LocalDateTime startDate, LocalDateTime endDate);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.dropContext IS NOT NULL AND pdl.dropContext != ''")
//    List<PieceDropLog> findWithContext();
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.contextData IS NOT NULL")
//    List<PieceDropLog> findWithContextData();
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.dropContext LIKE %:context%")
//    List<PieceDropLog> findByDropContextContaining(@Param("context") String context);
//
//    @Query("SELECT COUNT(pdl) FROM PieceDropLog pdl WHERE pdl.userId = :userId")
//    long countByUserId(@Param("userId") Long userId);
//
//    @Query("SELECT COUNT(pdl) FROM PieceDropLog pdl WHERE pdl.puzzlePieceId = :puzzlePieceId")
//    long countByPuzzlePieceId(@Param("puzzlePieceId") Integer puzzlePieceId);
//
//    @Query("SELECT COUNT(pdl) FROM PieceDropLog pdl WHERE pdl.dropSource = :dropSource")
//    long countByDropSource(@Param("dropSource") PieceDropLog.DropSource dropSource);
//
//    @Query("SELECT COUNT(pdl) FROM PieceDropLog pdl WHERE pdl.userId = :userId AND pdl.dropSource = :dropSource")
//    long countByUserIdAndDropSource(@Param("userId") Long userId, @Param("dropSource") PieceDropLog.DropSource dropSource);
//
//    @Query("SELECT COUNT(DISTINCT pdl.userId) FROM PieceDropLog pdl WHERE pdl.dropSource = :dropSource")
//    long countDistinctUsersByDropSource(@Param("dropSource") PieceDropLog.DropSource dropSource);
//
//    @Query("SELECT COUNT(DISTINCT pdl.puzzlePieceId) FROM PieceDropLog pdl WHERE pdl.userId = :userId")
//    long countDistinctPuzzlePiecesByUserId(@Param("userId") Long userId);
//
//    @Query("SELECT pdl.dropSource, COUNT(pdl) as dropCount, COUNT(DISTINCT pdl.userId) as userCount, " +
//           "MIN(pdl.droppedAt) as firstDrop, MAX(pdl.droppedAt) as lastDrop " +
//           "FROM PieceDropLog pdl GROUP BY pdl.dropSource")
//    List<Object[]> getDropStatsBySource();
//
//    @Query("SELECT pdl.dropSource, COUNT(pdl) as dropCount, MIN(pdl.droppedAt) as firstDrop, MAX(pdl.droppedAt) as lastDrop " +
//           "FROM PieceDropLog pdl WHERE pdl.userId = :userId GROUP BY pdl.dropSource")
//    List<Object[]> getDropStatsByUser(@Param("userId") Long userId);
//
//    @Query("SELECT pdl.puzzlePieceId, COUNT(pdl) as dropCount, COUNT(DISTINCT pdl.userId) as userCount " +
//           "FROM PieceDropLog pdl GROUP BY pdl.puzzlePieceId ORDER BY dropCount DESC")
//    List<Object[]> getDropStatsByPuzzlePiece();
//
//    @Query("SELECT DATE(pdl.droppedAt) as dropDate, COUNT(pdl) as dropCount " +
//           "FROM PieceDropLog pdl WHERE pdl.droppedAt >= :since GROUP BY DATE(pdl.droppedAt) ORDER BY dropDate")
//    List<Object[]> getDailyDropStatsSince(@Param("since") LocalDateTime since);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.userId = :userId ORDER BY pdl.droppedAt DESC")
//    List<PieceDropLog> findByUserIdOrderByDroppedAtDesc(@Param("userId") Long userId);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.puzzlePieceId = :puzzlePieceId ORDER BY pdl.droppedAt DESC")
//    List<PieceDropLog> findByPuzzlePieceIdOrderByDroppedAtDesc(@Param("puzzlePieceId") Integer puzzlePieceId);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.dropSource = :dropSource ORDER BY pdl.droppedAt DESC")
//    List<PieceDropLog> findByDropSourceOrderByDroppedAtDesc(@Param("dropSource") PieceDropLog.DropSource dropSource);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.droppedAt >= :since ORDER BY pdl.droppedAt DESC")
//    List<PieceDropLog> findRecentDrops(@Param("since") LocalDateTime since);
//
//    @Query("SELECT pdl FROM PieceDropLog pdl WHERE pdl.userId = :userId AND pdl.droppedAt >= :since ORDER BY pdl.droppedAt DESC")
//    List<PieceDropLog> findRecentDropsByUser(@Param("userId") Long userId, @Param("since") LocalDateTime since);
//}
