package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.UserCardsPieces;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserCardsPiecesRepository extends JpaRepository<UserCardsPieces, Long> {

    Optional<UserCardsPieces> findByUserId(Long userId);

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.piecesData IS NOT NULL")
    List<UserCardsPieces> findUsersWithData();

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.piecesData IS NULL OR JSON_LENGTH(ucp.piecesData) = 0")
    List<UserCardsPieces> findUsersWithoutData();

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.updatedAt >= :since")
    List<UserCardsPieces> findUpdatedSince(@Param("since") LocalDateTime since);

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.createdAt BETWEEN :startDate AND :endDate")
    List<UserCardsPieces> findCreatedBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.updatedAt BETWEEN :startDate AND :endDate")
    List<UserCardsPieces> findUpdatedBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(ucp) FROM UserCardsPieces ucp WHERE ucp.piecesData IS NOT NULL")
    long countUsersWithData();

    @Query("SELECT COUNT(ucp) FROM UserCardsPieces ucp WHERE ucp.createdAt >= :since")
    long countUsersCreatedSince(@Param("since") LocalDateTime since);

    @Query("SELECT COUNT(ucp) FROM UserCardsPieces ucp WHERE ucp.updatedAt >= :since")
    long countUsersUpdatedSince(@Param("since") LocalDateTime since);

    @Query("SELECT ucp.userId FROM UserCardsPieces ucp WHERE ucp.piecesData IS NOT NULL")
    List<Long> findUserIdsWithData();

    @Query("SELECT ucp.userId FROM UserCardsPieces ucp WHERE ucp.updatedAt >= :since")
    List<Long> findActiveUserIdsSince(@Param("since") LocalDateTime since);

    boolean existsByUserId(Long userId);

    void deleteByUserId(Long userId);

    @Query("SELECT ucp FROM UserCardsPieces ucp WHERE ucp.userId IN :userIds")
    List<UserCardsPieces> findByUserIdIn(@Param("userIds") List<Long> userIds);

    @Query("SELECT MAX(ucp.updatedAt) FROM UserCardsPieces ucp")
    Optional<LocalDateTime> findLastUpdateTime();

    @Query("SELECT MIN(ucp.createdAt) FROM UserCardsPieces ucp")
    Optional<LocalDateTime> findFirstCreationTime();
}
