package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.service.CardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/cards")
public class CardManagementController {

    private final CardService cardService;
    private final CardCollectionMapper mapper;

    public CardManagementController(CardService cardService, CardCollectionMapper mapper) {
        this.cardService = cardService;
        this.mapper = mapper;
    }

    @Operation(description = "Get all cards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getAllCards() {
        List<Card> cards = cardService.findAll();
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get cards with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<CardCollectionDto.CardResponse> getCardsPaged(@PageableDefault(size = 20) Pageable pageable) {
        return cardService.findAll(pageable)
                .map(mapper::toCardResponse);
    }

    @Operation(description = "Get cards by collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collection/{collectionId}")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCardsByCollection(@PathVariable Integer collectionId) {
        List<Card> cards = cardService.findByCollectionId(collectionId);
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get active cards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/active")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getActiveCards() {
        List<Card> cards = cardService.findActiveCards();
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get cards by rarity level", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/rarity/{rarityLevel}")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCardsByRarity(@PathVariable Byte rarityLevel) {
        List<Card> cards = cardService.findByRarityLevel(rarityLevel);
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get rare cards (rarity level 3)", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/rare")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getRareCards() {
        List<Card> cards = cardService.findRareCards();
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get common cards (rarity level 1)", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/common")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCommonCards() {
        List<Card> cards = cardService.findCommonCards();
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get uncommon cards (rarity level 2)", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/uncommon")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getUncommonCards() {
        List<Card> cards = cardService.findUncommonCards();
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get card by ID", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> getCardById(@PathVariable Integer id) {
        return cardService.findByIdWithRewards(id)
                .map(card -> ResponseEntity.ok(mapper.toCardResponse(card)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Search cards by name", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/search")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> searchCardsByName(@RequestParam String name) {
        List<Card> cards = cardService.findByName(name);
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Activate card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/activate")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> activateCard(@PathVariable Integer id) {
        return cardService.activate(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Disable card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/disable")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> disableCard(@PathVariable Integer id) {
        return cardService.disable(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Expire card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/expire")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> expireCard(@PathVariable Integer id) {
        return cardService.expire(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // Statistics endpoints
    @Operation(description = "Get card count by collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collection/{collectionId}/count")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Long> getCardCountByCollection(@PathVariable Integer collectionId) {
        long count = cardService.countByCollectionId(collectionId);
        return ResponseEntity.ok(count);
    }

    @Operation(description = "Get card count by rarity level", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/rarity/{rarityLevel}/count")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Long> getCardCountByRarity(@PathVariable Byte rarityLevel) {
        long count = cardService.countByRarityLevel(rarityLevel);
        return ResponseEntity.ok(count);
    }
}
