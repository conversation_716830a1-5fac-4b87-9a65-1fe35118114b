package com.ously.gamble.collectibles.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ously.gamble.collectibles.controller.UserCardsPiecesController.CreateUserCardsPiecesRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.UserCardsPieces;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.collectibles.persistence.repository.UserCardsPiecesRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserCardsPiecesService {

    private final UserCardsPiecesRepository userCardsPiecesRepository;
    private final CardRepository cardRepository;
    private final ObjectMapper objectMapper;

    public UserCardsPiecesService(UserCardsPiecesRepository userCardsPiecesRepository,
                                CardRepository cardRepository,
                                ObjectMapper objectMapper) {
        this.userCardsPiecesRepository = userCardsPiecesRepository;
        this.cardRepository = cardRepository;
        this.objectMapper = objectMapper;
    }

    @Transactional(readOnly = true)
    public List<UserCardsPieces> findAll() {
        return userCardsPiecesRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<UserCardsPieces> findAll(Pageable pageable) {
        return userCardsPiecesRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<UserCardsPieces> findByUserId(Long userId) {
        return userCardsPiecesRepository.findByUserId(userId);
    }

    @Transactional(readOnly = true)
    public Optional<Integer> getCollectionProgress(Long userId, Integer collectionId, int requiredPiecesPerCard) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> {
                    List<Card> cards = cardRepository.findByCardCollectionId(collectionId);
                    return userPieces.getCollectionProgress(collectionId, cards, requiredPiecesPerCard);
                });
    }

    @Transactional(readOnly = true)
    public Optional<Integer> getPiecesCount(Long userId, Integer collectionId, Integer cardId) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> userPieces.getPiecesCount(collectionId, cardId));
    }

    @Transactional(readOnly = true)
    public Optional<Boolean> isCardCompleted(Long userId, Integer collectionId, Integer cardId, int requiredPieces) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> userPieces.isCardCompleted(collectionId, cardId, requiredPieces));
    }

    @Transactional(readOnly = true)
    public Optional<Boolean> isCollectionCompleted(Long userId, Integer collectionId, int requiredPiecesPerCard) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> {
                    List<Card> cards = cardRepository.findByCardCollectionId(collectionId);
                    return userPieces.isCollectionCompleted(collectionId, cards, requiredPiecesPerCard);
                });
    }

    public UserCardsPieces createOrUpdate(CreateUserCardsPiecesRequest request) {
        Optional<UserCardsPieces> existing = userCardsPiecesRepository.findByUserId(request.userId());
        
        if (existing.isPresent()) {
            UserCardsPieces userPieces = existing.get();
            userPieces.setPiecesData(request.piecesData());
            return userCardsPiecesRepository.save(userPieces);
        } else {
            UserCardsPieces userPieces = new UserCardsPieces(request.userId(), request.piecesData());
            return userCardsPiecesRepository.save(userPieces);
        }
    }

    public Optional<UserCardsPieces> updatePiecesData(Long userId, JsonNode piecesData) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> {
                    userPieces.updatePiecesData(piecesData);
                    return userCardsPiecesRepository.save(userPieces);
                });
    }

    public Optional<UserCardsPieces> addPiecesToCard(Long userId, Integer collectionId, Integer cardId, int piecesToAdd) {
        Optional<UserCardsPieces> userPiecesOpt = userCardsPiecesRepository.findByUserId(userId);
        
        if (userPiecesOpt.isEmpty()) {
            UserCardsPieces newUserPieces = initializeUserData(userId);
            return Optional.of(addPiecesToExistingCard(newUserPieces, collectionId, cardId, piecesToAdd));
        } else {
            UserCardsPieces userPieces = userPiecesOpt.get();
            return Optional.of(addPiecesToExistingCard(userPieces, collectionId, cardId, piecesToAdd));
        }
    }

    private UserCardsPieces addPiecesToExistingCard(UserCardsPieces userPieces, Integer collectionId, Integer cardId, int piecesToAdd) {
        try {
            ObjectNode piecesData = (ObjectNode) userPieces.getPiecesData();
            if (piecesData == null) {
                piecesData = objectMapper.createObjectNode();
                userPieces.setPiecesData(piecesData);
            }

            if (!piecesData.has("collections")) {
                piecesData.set("collections", objectMapper.createObjectNode());
            }
            ObjectNode collections = (ObjectNode) piecesData.get("collections");

            String collectionIdStr = collectionId.toString();
            if (!collections.has(collectionIdStr)) {
                ObjectNode collection = objectMapper.createObjectNode();
                collection.set("cards", objectMapper.createObjectNode());
                collections.set(collectionIdStr, collection);
            }
            ObjectNode collection = (ObjectNode) collections.get(collectionIdStr);

            if (!collection.has("cards")) {
                collection.set("cards", objectMapper.createObjectNode());
            }
            ObjectNode cards = (ObjectNode) collection.get("cards");

            String cardIdStr = cardId.toString();
            if (!cards.has(cardIdStr)) {
                ObjectNode card = objectMapper.createObjectNode();
                card.put("pieces", piecesToAdd);
                cards.set(cardIdStr, card);
            } else {
                ObjectNode card = (ObjectNode) cards.get(cardIdStr);
                int currentPieces = card.path("pieces").asInt(0);
                card.put("pieces", currentPieces + piecesToAdd);
            }

            return userCardsPiecesRepository.save(userPieces);
        } catch (Exception e) {
            throw new RuntimeException("Failed to add pieces to card", e);
        }
    }

    public UserCardsPieces initializeUserData(Long userId) {
        if (userCardsPiecesRepository.existsByUserId(userId)) {
            return userCardsPiecesRepository.findByUserId(userId).get();
        }

        ObjectNode emptyData = objectMapper.createObjectNode();
        emptyData.set("collections", objectMapper.createObjectNode());
        
        UserCardsPieces userPieces = new UserCardsPieces(userId, emptyData);
        return userCardsPiecesRepository.save(userPieces);
    }

    public boolean delete(Long userId) {
        if (userCardsPiecesRepository.existsByUserId(userId)) {
            userCardsPiecesRepository.deleteByUserId(userId);
            return true;
        }
        return false;
    }

    @Transactional(readOnly = true)
    public boolean userHasData(Long userId) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(UserCardsPieces::hasData)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean userHasCollection(Long userId, Integer collectionId) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> userPieces.hasCollection(collectionId))
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean userHasCard(Long userId, Integer collectionId, Integer cardId) {
        return userCardsPiecesRepository.findByUserId(userId)
                .map(userPieces -> userPieces.hasCard(collectionId, cardId))
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public long countUsersWithData() {
        return userCardsPiecesRepository.countUsersWithData();
    }

    @Transactional(readOnly = true)
    public long countUsersCreatedSince(LocalDateTime since) {
        return userCardsPiecesRepository.countUsersCreatedSince(since);
    }

    @Transactional(readOnly = true)
    public long countUsersUpdatedSince(LocalDateTime since) {
        return userCardsPiecesRepository.countUsersUpdatedSince(since);
    }
}
