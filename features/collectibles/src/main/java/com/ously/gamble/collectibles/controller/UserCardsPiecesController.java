package com.ously.gamble.collectibles.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.UserCardsPieces;
import com.ously.gamble.collectibles.service.UserCardsPiecesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/user-cards-pieces")
public class UserCardsPiecesController {

    private final UserCardsPiecesService userCardsPiecesService;

    public UserCardsPiecesController(UserCardsPiecesService userCardsPiecesService) {
        this.userCardsPiecesService = userCardsPiecesService;
    }

    @Operation(description = "Get all user cards pieces", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<UserCardsPieces> getAllUserCardsPieces() {
        return userCardsPiecesService.findAll();
    }

    @Operation(description = "Get user cards pieces with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<UserCardsPieces> getUserCardsPiecesPaged(@PageableDefault(size = 20) Pageable pageable) {
        return userCardsPiecesService.findAll(pageable);
    }

    @Operation(description = "Get user cards pieces by user ID", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<UserCardsPieces> getUserCardsPiecesByUserId(@PathVariable Long userId) {
        return userCardsPiecesService.findByUserId(userId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Get user progress for collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/collection/{collectionId}/progress")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Integer> getUserCollectionProgress(
            @PathVariable Long userId,
            @PathVariable Integer collectionId,
            @RequestParam(defaultValue = "3") int requiredPiecesPerCard) {
        return userCardsPiecesService.getCollectionProgress(userId, collectionId, requiredPiecesPerCard)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Get user pieces count for specific card", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/collection/{collectionId}/card/{cardId}/pieces")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Integer> getUserCardPieces(
            @PathVariable Long userId,
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        return userCardsPiecesService.getPiecesCount(userId, collectionId, cardId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Check if user completed specific card", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/collection/{collectionId}/card/{cardId}/completed")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Boolean> isUserCardCompleted(
            @PathVariable Long userId,
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @RequestParam(defaultValue = "3") int requiredPieces) {
        return userCardsPiecesService.isCardCompleted(userId, collectionId, cardId, requiredPieces)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Check if user completed collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/{userId}/collection/{collectionId}/completed")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Boolean> isUserCollectionCompleted(
            @PathVariable Long userId,
            @PathVariable Integer collectionId,
            @RequestParam(defaultValue = "3") int requiredPiecesPerCard) {
        return userCardsPiecesService.isCollectionCompleted(userId, collectionId, requiredPiecesPerCard)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create or update user cards pieces", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public UserCardsPieces createOrUpdateUserCardsPieces(@RequestBody CreateUserCardsPiecesRequest request) {
        return userCardsPiecesService.createOrUpdate(request);
    }

    @Operation(description = "Update user pieces data", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/user/{userId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<UserCardsPieces> updateUserPiecesData(
            @PathVariable Long userId,
            @RequestBody UpdateUserPiecesDataRequest request) {
        return userCardsPiecesService.updatePiecesData(userId, request.piecesData())
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Add pieces to user card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/user/{userId}/collection/{collectionId}/card/{cardId}/add-pieces")
    @RolesAllowed("ADMIN")
    public ResponseEntity<UserCardsPieces> addPiecesToUserCard(
            @PathVariable Long userId,
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @RequestParam int piecesToAdd) {
        return userCardsPiecesService.addPiecesToCard(userId, collectionId, cardId, piecesToAdd)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Initialize empty data for user", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/user/{userId}/initialize")
    @RolesAllowed("ADMIN")
    public UserCardsPieces initializeUserData(@PathVariable Long userId) {
        return userCardsPiecesService.initializeUserData(userId);
    }

    @Operation(description = "Delete user cards pieces", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/user/{userId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteUserCardsPieces(@PathVariable Long userId) {
        if (userCardsPiecesService.delete(userId)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // DTOs
    public record CreateUserCardsPiecesRequest(
            Long userId,
            JsonNode piecesData
    ) {}

    public record UpdateUserPiecesDataRequest(
            JsonNode piecesData
    ) {}
}
