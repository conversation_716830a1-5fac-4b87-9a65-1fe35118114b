package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "piece_drop_log")
public class PieceDropLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "puzzle_piece_id", nullable = false)
    private Integer puzzlePieceId;

    @Enumerated(EnumType.STRING)
    @Column(name = "drop_source", nullable = false)
    private DropSource dropSource;

    @Column(name = "drop_context", length = 100)
    private String dropContext;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "context_data", columnDefinition = "JSON")
    private JsonNode contextData;

    @CreationTimestamp
    @Column(name = "dropped_at", updatable = false)
    private LocalDateTime droppedAt;

    public enum DropSource {
        LOOTBOX, GAME_PROGRESS, EVENT, PURCHASE, EXCHANGED
    }

    // Rich Domain Model methods
    public boolean isFromLootbox() {
        return dropSource == DropSource.LOOTBOX;
    }

    public boolean isFromGameProgress() {
        return dropSource == DropSource.GAME_PROGRESS;
    }

    public boolean isFromEvent() {
        return dropSource == DropSource.EVENT;
    }

    public boolean isFromPurchase() {
        return dropSource == DropSource.PURCHASE;
    }

    public boolean isFromExchange() {
        return dropSource == DropSource.EXCHANGED;
    }

    public boolean hasContext() {
        return dropContext != null && !dropContext.trim().isEmpty();
    }

    public boolean hasContextData() {
        return contextData != null && !contextData.isNull() && !contextData.isEmpty();
    }

    public String getFullDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Piece ").append(puzzlePieceId)
            .append(" dropped from ").append(dropSource.name().toLowerCase());
        
        if (hasContext()) {
            desc.append(" (").append(dropContext).append(")");
        }
        
        return desc.toString();
    }

    public void validateDrop() {
        if (userId == null) {
            throw new IllegalStateException("User ID is required");
        }
        if (puzzlePieceId == null) {
            throw new IllegalStateException("Puzzle piece ID is required");
        }
        if (dropSource == null) {
            throw new IllegalStateException("Drop source is required");
        }
    }

    // Constructors
    public PieceDropLog() {}

    public PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource) {
        this.userId = userId;
        this.puzzlePieceId = puzzlePieceId;
        this.dropSource = dropSource;
        validateDrop();
    }

    public PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource, String dropContext) {
        this.userId = userId;
        this.puzzlePieceId = puzzlePieceId;
        this.dropSource = dropSource;
        this.dropContext = dropContext;
        validateDrop();
    }

    public PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource, String dropContext, JsonNode contextData) {
        this.userId = userId;
        this.puzzlePieceId = puzzlePieceId;
        this.dropSource = dropSource;
        this.dropContext = dropContext;
        this.contextData = contextData;
        validateDrop();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { 
        this.userId = userId;
        validateDrop();
    }

    public Integer getPuzzlePieceId() { return puzzlePieceId; }
    public void setPuzzlePieceId(Integer puzzlePieceId) { 
        this.puzzlePieceId = puzzlePieceId;
        validateDrop();
    }

    public DropSource getDropSource() { return dropSource; }
    public void setDropSource(DropSource dropSource) { 
        this.dropSource = dropSource;
        validateDrop();
    }

    public String getDropContext() { return dropContext; }
    public void setDropContext(String dropContext) { this.dropContext = dropContext; }

    public JsonNode getContextData() { return contextData; }
    public void setContextData(JsonNode contextData) { this.contextData = contextData; }

    public LocalDateTime getDroppedAt() { return droppedAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PieceDropLog that = (PieceDropLog) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "PieceDropLog{" +
                "id=" + id +
                ", userId=" + userId +
                ", puzzlePieceId=" + puzzlePieceId +
                ", dropSource=" + dropSource +
                ", dropContext='" + dropContext + '\'' +
                ", droppedAt=" + droppedAt +
                '}';
    }
}
