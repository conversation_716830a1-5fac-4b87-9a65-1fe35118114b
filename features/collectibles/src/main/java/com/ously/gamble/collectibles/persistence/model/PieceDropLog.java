package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "piece_drop_log")
public class PieceDropLog {
    public enum DropSource {
        LOOTBOX, GAME_PROGRESS, EVENT, PURCHASE, EXCHANGED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "puzzle_piece_id", nullable = false)
    private Integer puzzlePieceId;

    @Enumerated(EnumType.STRING)
    @Column(name = "drop_source", nullable = false)
    private DropSource dropSource;

    @Column(name = "drop_context", length = 100)
    private String dropContext;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "context_data", columnDefinition = "JSON")
    private JsonNode contextData;

    @Column(name = "dropped_at", updatable = false)
    private LocalDateTime droppedAt = LocalDateTime.now();

    // Constructors
    protected PieceDropLog() {}

    protected PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.puzzlePieceId = Objects.requireNonNull(puzzlePieceId, "Puzzle piece ID cannot be null");
        this.dropSource = Objects.requireNonNull(dropSource, "Drop source cannot be null");
        validateDrop();
    }

    protected PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource, String dropContext) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.puzzlePieceId = Objects.requireNonNull(puzzlePieceId, "Puzzle piece ID cannot be null");
        this.dropSource = Objects.requireNonNull(dropSource, "Drop source cannot be null");
        this.dropContext = dropContext;
        validateDrop();
    }

    protected PieceDropLog(Long userId, Integer puzzlePieceId, DropSource dropSource,
                         String dropContext, JsonNode contextData) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.puzzlePieceId = Objects.requireNonNull(puzzlePieceId, "Puzzle piece ID cannot be null");
        this.dropSource = Objects.requireNonNull(dropSource, "Drop source cannot be null");
        this.dropContext = dropContext;
        this.contextData = contextData;
        validateDrop();
    }

    // Factory methods
    public static PieceDropLog logDrop(Long userId, Integer puzzlePieceId, DropSource dropSource) {
        return new PieceDropLog(userId, puzzlePieceId, dropSource);
    }

    public static PieceDropLog logDropWithContext(Long userId, Integer puzzlePieceId,
                                                DropSource dropSource, String context) {
        return new PieceDropLog(userId, puzzlePieceId, dropSource, context);
    }

    public static PieceDropLog logDropWithFullContext(Long userId, Integer puzzlePieceId,
                                                    DropSource dropSource, String context, JsonNode contextData) {
        return new PieceDropLog(userId, puzzlePieceId, dropSource, context, contextData);
    }

    // Rich Domain Model methods
    public boolean isFromLootbox() {
        return dropSource == DropSource.LOOTBOX;
    }

    public boolean isFromGameProgress() {
        return dropSource == DropSource.GAME_PROGRESS;
    }

    public boolean isFromEvent() {
        return dropSource == DropSource.EVENT;
    }

    public boolean isFromPurchase() {
        return dropSource == DropSource.PURCHASE;
    }

    public boolean isFromExchange() {
        return dropSource == DropSource.EXCHANGED;
    }

    public boolean hasContext() {
        return dropContext != null && !dropContext.trim().isEmpty();
    }

    public boolean hasContextData() {
        return contextData != null && !contextData.isNull() && !contextData.isEmpty();
    }

    public String getFullDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Piece ").append(puzzlePieceId)
            .append(" dropped from ").append(dropSource.name().toLowerCase());

        if (hasContext()) {
            desc.append(" (").append(dropContext).append(")");
        }

        return desc.toString();
    }

    private void validateDrop() {
        // Validation is done in constructor via Objects.requireNonNull
    }

    // Getters
    public Long getId() {
        return id;
    }

    public Long getUserId() {
        return userId;
    }

    public Integer getPuzzlePieceId() {
        return puzzlePieceId;
    }

    public DropSource getDropSource() {
        return dropSource;
    }

    public String getDropContext() {
        return dropContext;
    }

    public JsonNode getContextData() {
        return contextData;
    }

    public LocalDateTime getDroppedAt() {
        return droppedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PieceDropLog that = (PieceDropLog) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "PieceDropLog{" +
                "id=" + id +
                ", userId=" + userId +
                ", puzzlePieceId=" + puzzlePieceId +
                ", dropSource=" + dropSource +
                ", dropContext='" + dropContext + '\'' +
                ", droppedAt=" + droppedAt +
                '}';
    }
}
