package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.CollectionRewardController.CreateCollectionRewardRequest;
import com.ously.gamble.collectibles.controller.CollectionRewardController.UpdateCollectionRewardRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.CollectionReward;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.collectibles.persistence.repository.CollectionRewardRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CollectionRewardService {

    private final CollectionRewardRepository collectionRewardRepository;
    private final CardCollectionRepository cardCollectionRepository;
    private final CardRepository cardRepository;

    public CollectionRewardService(CollectionRewardRepository collectionRewardRepository,
                                 CardCollectionRepository cardCollectionRepository,
                                 CardRepository cardRepository) {
        this.collectionRewardRepository = collectionRewardRepository;
        this.cardCollectionRepository = cardCollectionRepository;
        this.cardRepository = cardRepository;
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findAll() {
        return collectionRewardRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<CollectionReward> findAll(Pageable pageable) {
        return collectionRewardRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CollectionReward> findById(Integer id) {
        return collectionRewardRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<CollectionReward> findByIdWithDetails(Integer id) {
        return Optional.ofNullable(collectionRewardRepository.findByIdWithCollectionAndCard(id));
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findByCollectionId(Integer collectionId) {
        return collectionRewardRepository.findByCardCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findByCardId(Integer cardId) {
        return collectionRewardRepository.findByCardId(cardId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findCompletionRewards() {
        return collectionRewardRepository.findCompletionRewards();
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findMilestoneRewards() {
        return collectionRewardRepository.findMilestoneRewards();
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findMilestoneRewardsByPercentage(Byte percentage) {
        return collectionRewardRepository.findMilestoneRewardsByPercentage(percentage);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findCompletionRewardsByCollection(Integer collectionId) {
        return collectionRewardRepository.findCompletionRewardsByCollection(collectionId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findMilestoneRewardsByCollection(Integer collectionId) {
        return collectionRewardRepository.findMilestoneRewardsByCollection(collectionId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findCompletionRewardsByCard(Integer cardId) {
        return collectionRewardRepository.findCompletionRewardsByCard(cardId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findMilestoneRewardsByCard(Integer cardId) {
        return collectionRewardRepository.findMilestoneRewardsByCard(cardId);
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findCollectionRewards() {
        return collectionRewardRepository.findCollectionRewards();
    }

    @Transactional(readOnly = true)
    public List<CollectionReward> findCardRewards() {
        return collectionRewardRepository.findCardRewards();
    }

    @Transactional(readOnly = true)
    public List<Byte> findDistinctMilestonePercentages() {
        return collectionRewardRepository.findDistinctMilestonePercentages();
    }

    @Transactional(readOnly = true)
    public long countByCollectionId(Integer collectionId) {
        return collectionRewardRepository.countByCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public long countByCardId(Integer cardId) {
        return collectionRewardRepository.countByCardId(cardId);
    }

    public CollectionReward create(CreateCollectionRewardRequest request) {
        validateCreateRequest(request);

        CollectionReward reward;
        
        if (request.cardCollectionId() != null) {
            CardCollection collection = cardCollectionRepository.findById(request.cardCollectionId())
                    .orElseThrow(() -> new IllegalArgumentException("Card collection not found: " + request.cardCollectionId()));
            
            if (request.rewardType() == CollectionReward.RewardType.MILESTONE) {
                reward = new CollectionReward(collection, request.rewardType(), request.milestonePercentage(), request.rewardData());
            } else {
                reward = new CollectionReward(collection, request.rewardType(), request.rewardData());
            }
        } else {
            Card card = cardRepository.findById(request.cardId())
                    .orElseThrow(() -> new IllegalArgumentException("Card not found: " + request.cardId()));
            
            if (request.rewardType() == CollectionReward.RewardType.MILESTONE) {
                reward = new CollectionReward(card, request.rewardType(), request.milestonePercentage(), request.rewardData());
            } else {
                reward = new CollectionReward(card, request.rewardType(), request.rewardData());
            }
        }
        
        return collectionRewardRepository.save(reward);
    }

    public Optional<CollectionReward> update(Integer id, UpdateCollectionRewardRequest request) {
        return collectionRewardRepository.findById(id)
                .map(reward -> {
                    if (request.rewardType() != null) {
                        reward.setRewardType(request.rewardType());
                    }
                    if (request.milestonePercentage() != null) {
                        reward.setMilestonePercentage(request.milestonePercentage());
                    }
                    if (request.rewardData() != null) {
                        reward.setRewardData(request.rewardData());
                    }
                    return collectionRewardRepository.save(reward);
                });
    }

    public boolean delete(Integer id) {
        if (collectionRewardRepository.existsById(id)) {
            collectionRewardRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private void validateCreateRequest(CreateCollectionRewardRequest request) {
        if (request.cardCollectionId() == null && request.cardId() == null) {
            throw new IllegalArgumentException("Either cardCollectionId or cardId must be provided");
        }
        if (request.cardCollectionId() != null && request.cardId() != null) {
            throw new IllegalArgumentException("Cannot specify both cardCollectionId and cardId");
        }
        if (request.rewardType() == CollectionReward.RewardType.MILESTONE && request.milestonePercentage() == null) {
            throw new IllegalArgumentException("Milestone percentage is required for milestone rewards");
        }
        if (request.rewardType() == CollectionReward.RewardType.COMPLETION && request.milestonePercentage() != null) {
            throw new IllegalArgumentException("Milestone percentage should not be provided for completion rewards");
        }
        if (request.rewardData() == null) {
            throw new IllegalArgumentException("Reward data is required");
        }
    }

    @Transactional(readOnly = true)
    public boolean isRewardForCollection(Integer id) {
        return collectionRewardRepository.findById(id)
                .map(CollectionReward::isForCollection)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isRewardForCard(Integer id) {
        return collectionRewardRepository.findById(id)
                .map(CollectionReward::isForCard)
                .orElse(false);
    }
}
