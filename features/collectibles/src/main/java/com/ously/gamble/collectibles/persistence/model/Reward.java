package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Objects;

@Entity
@Table(name = "rewards")
public class Reward {
    public enum RewardType {
        COMPLETION, MILESTONE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_collection_id")
    private CardCollection cardCollection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_id")
    private Card card;

    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type", nullable = false)
    private RewardType rewardType;

    @Column(name = "milestone_percentage")
    private Byte milestonePercentage;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "reward_data", nullable = false, columnDefinition = "JSON")
    private JsonNode rewardData;

    // Constructors
    protected Reward() {}

    protected Reward(CardCollection cardCollection, RewardType rewardType, JsonNode rewardData) {
        this.cardCollection = Objects.requireNonNull(cardCollection, "Card collection cannot be null");
        this.rewardType = Objects.requireNonNull(rewardType, "Reward type cannot be null");
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateReward();
    }

    protected Reward(Card card, RewardType rewardType, JsonNode rewardData) {
        this.card = Objects.requireNonNull(card, "Card cannot be null");
        this.rewardType = Objects.requireNonNull(rewardType, "Reward type cannot be null");
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateReward();
    }

    protected Reward(CardCollection cardCollection, RewardType rewardType,
                             Byte milestonePercentage, JsonNode rewardData) {
        this.cardCollection = Objects.requireNonNull(cardCollection, "Card collection cannot be null");
        this.rewardType = Objects.requireNonNull(rewardType, "Reward type cannot be null");
        this.milestonePercentage = milestonePercentage;
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateReward();
    }

    protected Reward(Card card, RewardType rewardType,
                             Byte milestonePercentage, JsonNode rewardData) {
        this.card = Objects.requireNonNull(card, "Card cannot be null");
        this.rewardType = Objects.requireNonNull(rewardType, "Reward type cannot be null");
        this.milestonePercentage = milestonePercentage;
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateReward();
    }

    // Factory methods
    public static Reward createCompletionRewardForCollection(CardCollection collection, JsonNode rewardData) {
        return new Reward(collection, RewardType.COMPLETION, rewardData);
    }

    public static Reward createMilestoneRewardForCollection(CardCollection collection,
                                                                    Byte percentage, JsonNode rewardData) {
        return new Reward(collection, RewardType.MILESTONE, percentage, rewardData);
    }

    public static Reward createCompletionRewardForCard(Card card, JsonNode rewardData) {
        return new Reward(card, RewardType.COMPLETION, rewardData);
    }

    public static Reward createMilestoneRewardForCard(Card card, Byte percentage, JsonNode rewardData) {
        return new Reward(card, RewardType.MILESTONE, percentage, rewardData);
    }

    // Rich Domain Model methods
    public boolean isForCollection() {
        return cardCollection != null && card == null;
    }

    public boolean isForCard() {
        return card != null && cardCollection == null;
    }

    public boolean isCompletionReward() {
        return rewardType == RewardType.COMPLETION;
    }

    public boolean isMilestoneReward() {
        return rewardType == RewardType.MILESTONE;
    }

    public String getTargetName() {
        if (isForCollection()) {
            return cardCollection.getName();
        } else if (isForCard()) {
            return card.getName();
        }
        return "Unknown";
    }

    public Integer getTargetId() {
        if (isForCollection()) {
            return cardCollection.getId();
        } else if (isForCard()) {
            return card.getId();
        }
        return null;
    }

    // Modifiers
    public void changeRewardData(JsonNode newRewardData) {
        this.rewardData = Objects.requireNonNull(newRewardData, "Reward data cannot be null");
    }

    public void changeMilestonePercentage(Byte newPercentage) {
        if (rewardType != RewardType.MILESTONE) {
            throw new IllegalStateException("Cannot set milestone percentage for non-milestone reward");
        }
        this.milestonePercentage = newPercentage;
        validateMilestonePercentage();
    }

    // Invariant validation methods
    private void validateReward() {
        validateTargetAssociation();
        validateRewardTypeConsistency();
        validateMilestonePercentage();
        validateBusinessRules();
    }

    private void validateTargetAssociation() {
        if (cardCollection == null && card == null) {
            throw new IllegalStateException("Reward must be associated with either collection or card");
        }
        if (cardCollection != null && card != null) {
            throw new IllegalStateException("Reward cannot be associated with both collection and card");
        }
    }

    private void validateRewardTypeConsistency() {
        if (rewardType == RewardType.MILESTONE && milestonePercentage == null) {
            throw new IllegalStateException("Milestone reward must have percentage");
        }
        if (rewardType == RewardType.COMPLETION && milestonePercentage != null) {
            throw new IllegalStateException("Completion reward cannot have percentage");
        }
    }

    private void validateMilestonePercentage() {
        if (milestonePercentage != null && (milestonePercentage < 1 || milestonePercentage > 100)) {
            throw new IllegalStateException("Milestone percentage must be between 1 and 100");
        }
    }

    private void validateBusinessRules() {
        // Business rule: Milestone percentages should be meaningful intervals
        if (rewardType == RewardType.MILESTONE && milestonePercentage != null) {
            if (milestonePercentage % 5 != 0) {
                throw new IllegalArgumentException("Milestone percentage should be in 5% intervals");
            }
        }
    }

    // Getters
    public Integer getId() { 
        return id; 
    }

    public CardCollection getCardCollection() { 
        return cardCollection; 
    }

    public Card getCard() { 
        return card; 
    }

    public RewardType getRewardType() { 
        return rewardType; 
    }

    public Byte getMilestonePercentage() { 
        return milestonePercentage; 
    }

    public JsonNode getRewardData() { 
        return rewardData; 
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Reward that = (Reward) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Reward{" +
                "id=" + id +
                ", rewardType=" + rewardType +
                ", milestonePercentage=" + milestonePercentage +
                ", target=" + getTargetName() +
                '}';
    }
}
