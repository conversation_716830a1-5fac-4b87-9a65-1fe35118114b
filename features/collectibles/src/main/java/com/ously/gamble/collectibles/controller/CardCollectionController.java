//package com.ously.gamble.collectibles.controller;
//
//import com.ously.gamble.collectibles.persistence.model.CardCollection;
//import com.ously.gamble.collectibles.service.CardCollectionService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.security.SecurityRequirement;
//import jakarta.annotation.security.RolesAllowed;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.web.PageableDefault;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/api/admin/card-collections")
//public class CardCollectionController {
//
//    private final CardCollectionService cardCollectionService;
//
//    public CardCollectionController(CardCollectionService cardCollectionService) {
//        this.cardCollectionService = cardCollectionService;
//    }
//
//    @Operation(description = "Get all card collections", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping
//    @RolesAllowed("ADMIN")
//    public List<CardCollection> getAllCollections() {
//        return cardCollectionService.findAll();
//    }
//
//    @Operation(description = "Get card collections with pagination", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/paged")
//    @RolesAllowed("ADMIN")
//    public Page<CardCollection> getCollectionsPaged(@PageableDefault(size = 20) Pageable pageable) {
//        return cardCollectionService.findAll(pageable);
//    }
//
//    @Operation(description = "Get active card collections", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/active")
//    @RolesAllowed("ADMIN")
//    public List<CardCollection> getActiveCollections() {
//        return cardCollectionService.findActiveCollections();
//    }
//
//    @Operation(description = "Get card collection by ID", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollection> getCollectionById(@PathVariable Integer id) {
//        return cardCollectionService.findById(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Create new card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping
//    @RolesAllowed("ADMIN")
//    public CardCollection createCollection(@RequestBody CreateCardCollectionRequest request) {
//        return cardCollectionService.create(request);
//    }
//
//    @Operation(description = "Update card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PutMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollection> updateCollection(
//            @PathVariable Integer id,
//            @RequestBody UpdateCardCollectionRequest request) {
//        return cardCollectionService.update(id, request)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Delete card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @DeleteMapping("/{id}")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
//        if (cardCollectionService.delete(id)) {
//            return ResponseEntity.noContent().build();
//        }
//        return ResponseEntity.notFound().build();
//    }
//
//    @Operation(description = "Activate card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{id}/activate")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollection> activateCollection(@PathVariable Integer id) {
//        return cardCollectionService.activate(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Disable card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{id}/disable")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollection> disableCollection(@PathVariable Integer id) {
//        return cardCollectionService.disable(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Expire card collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @PostMapping("/{id}/expire")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<CardCollection> expireCollection(@PathVariable Integer id) {
//        return cardCollectionService.expire(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @Operation(description = "Get cards in collection", security = {@SecurityRequirement(name = "bearer-key")})
//    @GetMapping("/{id}/cards")
//    @RolesAllowed("ADMIN")
//    public ResponseEntity<List<com.ously.gamble.collectibles.persistence.model.Card>> getCollectionCards(@PathVariable Integer id) {
//        return cardCollectionService.findById(id)
//                .map(collection -> ResponseEntity.ok(collection.getCards()))
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    // DTOs
//    public record CreateCardCollectionRequest(
//            String name,
//            String description,
//            java.time.LocalDateTime startDate,
//            java.time.LocalDateTime endDate,
//            Integer sortOrder
//    ) {}
//
//    public record UpdateCardCollectionRequest(
//            String name,
//            String description,
//            java.time.LocalDateTime startDate,
//            java.time.LocalDateTime endDate,
//            CardCollection.CollectionStatus status,
//            Integer sortOrder
//    ) {}
//}
