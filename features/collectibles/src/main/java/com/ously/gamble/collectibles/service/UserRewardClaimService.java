package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.UserRewardClaimController.CreateUserRewardClaimRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.CollectionReward;
import com.ously.gamble.collectibles.persistence.model.UserRewardClaim;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.collectibles.persistence.repository.CollectionRewardRepository;
import com.ously.gamble.collectibles.persistence.repository.UserRewardClaimRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserRewardClaimService {

    private final UserRewardClaimRepository userRewardClaimRepository;
    private final CollectionRewardRepository collectionRewardRepository;
    private final CardCollectionRepository cardCollectionRepository;
    private final CardRepository cardRepository;

    public UserRewardClaimService(UserRewardClaimRepository userRewardClaimRepository,
                                CollectionRewardRepository collectionRewardRepository,
                                CardCollectionRepository cardCollectionRepository,
                                CardRepository cardRepository) {
        this.userRewardClaimRepository = userRewardClaimRepository;
        this.collectionRewardRepository = collectionRewardRepository;
        this.cardCollectionRepository = cardCollectionRepository;
        this.cardRepository = cardRepository;
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findAll() {
        return userRewardClaimRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<UserRewardClaim> findAll(Pageable pageable) {
        return userRewardClaimRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<UserRewardClaim> findById(Long id) {
        return userRewardClaimRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<UserRewardClaim> findByIdWithDetails(Long id) {
        return Optional.ofNullable(userRewardClaimRepository.findByIdWithDetails(id));
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findActiveClaims() {
        return userRewardClaimRepository.findActiveClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findDeletedClaims() {
        return userRewardClaimRepository.findDeletedClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByUserId(Long userId) {
        return userRewardClaimRepository.findByUserId(userId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findActiveClaimsByUserId(Long userId) {
        return userRewardClaimRepository.findActiveClaimsByUserId(userId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findDeletedClaimsByUserId(Long userId) {
        return userRewardClaimRepository.findDeletedClaimsByUserId(userId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByCollectionId(Integer collectionId) {
        return userRewardClaimRepository.findByCardCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findActiveClaimsByCollectionId(Integer collectionId) {
        return userRewardClaimRepository.findActiveClaimsByCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByCardId(Integer cardId) {
        return userRewardClaimRepository.findByCardId(cardId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findActiveClaimsByCardId(Integer cardId) {
        return userRewardClaimRepository.findActiveClaimsByCardId(cardId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByRewardId(Integer rewardId) {
        return userRewardClaimRepository.findByCollectionRewardId(rewardId);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findCompletionClaims() {
        return userRewardClaimRepository.findCompletionClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findMilestoneClaims() {
        return userRewardClaimRepository.findMilestoneClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findCollectionRewardClaims() {
        return userRewardClaimRepository.findCollectionRewardClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findCardRewardClaims() {
        return userRewardClaimRepository.findCardRewardClaims();
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return userRewardClaimRepository.findByClaimedAtBetween(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public List<UserRewardClaim> findByUserIdAndDateRange(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        return userRewardClaimRepository.findByUserIdAndClaimedAtBetween(userId, startDate, endDate);
    }

    @Transactional(readOnly = true)
    public long countActiveClaimsByUserId(Long userId) {
        return userRewardClaimRepository.countActiveClaimsByUserId(userId);
    }

    @Transactional(readOnly = true)
    public long countActiveClaimsByCollectionId(Integer collectionId) {
        return userRewardClaimRepository.countActiveClaimsByCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public long countActiveClaimsByCardId(Integer cardId) {
        return userRewardClaimRepository.countActiveClaimsByCardId(cardId);
    }

    @Transactional(readOnly = true)
    public long countActiveClaimsByRewardId(Integer rewardId) {
        return userRewardClaimRepository.countActiveClaimsByRewardId(rewardId);
    }

    @Transactional(readOnly = true)
    public long countDistinctActiveUsers() {
        return userRewardClaimRepository.countDistinctActiveUsers();
    }

    @Transactional(readOnly = true)
    public long countDistinctActiveUsersByCollectionId(Integer collectionId) {
        return userRewardClaimRepository.countDistinctActiveUsersByCollectionId(collectionId);
    }

    public UserRewardClaim create(CreateUserRewardClaimRequest request) {
        validateCreateRequest(request);

        // Check for existing claim
        List<UserRewardClaim> existingClaims = userRewardClaimRepository.findExistingClaim(
                request.userId(), request.collectionRewardId(), 
                request.cardCollectionId(), request.cardId());
        
        if (!existingClaims.isEmpty()) {
            throw new IllegalStateException("User has already claimed this reward");
        }

        CollectionReward collectionReward = collectionRewardRepository.findById(request.collectionRewardId())
                .orElseThrow(() -> new IllegalArgumentException("Collection reward not found: " + request.collectionRewardId()));

        CardCollection cardCollection = cardCollectionRepository.findById(request.cardCollectionId())
                .orElseThrow(() -> new IllegalArgumentException("Card collection not found: " + request.cardCollectionId()));

        UserRewardClaim claim;
        if (request.cardId() != null) {
            Card card = cardRepository.findById(request.cardId())
                    .orElseThrow(() -> new IllegalArgumentException("Card not found: " + request.cardId()));
            claim = new UserRewardClaim(request.userId(), collectionReward, cardCollection, card, request.rewardData());
        } else {
            claim = new UserRewardClaim(request.userId(), collectionReward, cardCollection, request.rewardData());
        }

        return userRewardClaimRepository.save(claim);
    }

    public Optional<UserRewardClaim> markAsDeleted(Long id) {
        return userRewardClaimRepository.findById(id)
                .map(claim -> {
                    claim.markAsDeleted();
                    return userRewardClaimRepository.save(claim);
                });
    }

    public Optional<UserRewardClaim> restore(Long id) {
        return userRewardClaimRepository.findById(id)
                .map(claim -> {
                    claim.restore();
                    return userRewardClaimRepository.save(claim);
                });
    }

    public boolean delete(Long id) {
        if (userRewardClaimRepository.existsById(id)) {
            userRewardClaimRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private void validateCreateRequest(CreateUserRewardClaimRequest request) {
        if (request.userId() == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (request.collectionRewardId() == null) {
            throw new IllegalArgumentException("Collection reward ID is required");
        }
        if (request.cardCollectionId() == null) {
            throw new IllegalArgumentException("Card collection ID is required");
        }
        if (request.rewardData() == null) {
            throw new IllegalArgumentException("Reward data is required");
        }
    }

    @Transactional(readOnly = true)
    public boolean hasUserClaimedReward(Long userId, Integer rewardId, Integer collectionId, Integer cardId) {
        return userRewardClaimRepository.existsByUserIdAndCollectionRewardIdAndCardCollectionIdAndCardIdAndDeletedAtIsNull(
                userId, rewardId, collectionId, cardId);
    }

    @Transactional(readOnly = true)
    public boolean isClaimActive(Long id) {
        return userRewardClaimRepository.findById(id)
                .map(UserRewardClaim::isActive)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isClaimDeleted(Long id) {
        return userRewardClaimRepository.findById(id)
                .map(UserRewardClaim::isDeleted)
                .orElse(true);
    }
}
