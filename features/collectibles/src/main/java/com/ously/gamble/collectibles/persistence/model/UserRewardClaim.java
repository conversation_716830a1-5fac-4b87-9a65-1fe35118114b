package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "user_reward_claims")
public class UserRewardClaim {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "collection_reward_id", nullable = false)
    private Reward collectionReward;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_collection_id", nullable = false)
    private CardCollection cardCollection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_id")
    private Card card;

    @Column(name = "claimed_at", updatable = false)
    private LocalDateTime claimedAt = LocalDateTime.now();

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "reward_data", nullable = false, columnDefinition = "JSON")
    private JsonNode rewardData;

    // Constructors
    protected UserRewardClaim() {}

    protected UserRewardClaim(Long userId, Reward collectionReward,
                            CardCollection cardCollection, JsonNode rewardData) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.collectionReward = Objects.requireNonNull(collectionReward, "Collection reward cannot be null");
        this.cardCollection = Objects.requireNonNull(cardCollection, "Card collection cannot be null");
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateClaim();
    }

    protected UserRewardClaim(Long userId, Reward collectionReward,
                            CardCollection cardCollection, Card card, JsonNode rewardData) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.collectionReward = Objects.requireNonNull(collectionReward, "Collection reward cannot be null");
        this.cardCollection = Objects.requireNonNull(cardCollection, "Card collection cannot be null");
        this.card = card;
        this.rewardData = Objects.requireNonNull(rewardData, "Reward data cannot be null");
        validateClaim();
    }

    // Factory methods
    public static UserRewardClaim claimCollectionReward(Long userId, Reward collectionReward,
                                                      CardCollection cardCollection, JsonNode rewardData) {
        return new UserRewardClaim(userId, collectionReward, cardCollection, rewardData);
    }

    public static UserRewardClaim claimCardReward(Long userId, Reward collectionReward,
                                                CardCollection cardCollection, Card card, JsonNode rewardData) {
        return new UserRewardClaim(userId, collectionReward, cardCollection, card, rewardData);
    }

    // Rich Domain Model methods
    public boolean isActive() {
        return deletedAt == null;
    }

    public boolean isDeleted() {
        return deletedAt != null;
    }

    // Modifiers
    public void markAsDeleted() {
        if (isDeleted()) {
            throw new IllegalStateException("Claim is already deleted");
        }
        this.deletedAt = LocalDateTime.now();
    }

    public void restore() {
        if (!isDeleted()) {
            throw new IllegalStateException("Claim is not deleted");
        }
        this.deletedAt = null;
    }

    public boolean isForCollection() {
        return collectionReward.isForCollection();
    }

    public boolean isForCard() {
        return collectionReward.isForCard();
    }

    public boolean isCompletionReward() {
        return collectionReward.isCompletionReward();
    }

    public boolean isMilestoneReward() {
        return collectionReward.isMilestoneReward();
    }

    public String getRewardDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append(collectionReward.getRewardType().name().toLowerCase());

        if (isMilestoneReward()) {
            desc.append(" (").append(collectionReward.getMilestonePercentage()).append("%)");
        }

        desc.append(" reward for ");

        if (isForCollection()) {
            desc.append("collection: ").append(cardCollection.getName());
        } else if (isForCard()) {
            desc.append("card: ").append(card.getName());
        }

        return desc.toString();
    }

    public void validateClaim() {
        if (collectionReward == null) {
            throw new IllegalStateException("Collection reward is required");
        }
        if (cardCollection == null) {
            throw new IllegalStateException("Card collection is required");
        }
        if (isForCard() && card == null) {
            throw new IllegalStateException("Card is required for card rewards");
        }
        if (isForCollection() && card != null) {
            throw new IllegalStateException("Card should be null for collection rewards");
        }
        if (rewardData == null) {
            throw new IllegalStateException("Reward data is required");
        }
    }

    // Getters
    public Long getId() {
        return id;
    }

    public Long getUserId() {
        return userId;
    }

    public Reward getCollectionReward() {
        return collectionReward;
    }

    public CardCollection getCardCollection() {
        return cardCollection;
    }

    public Card getCard() {
        return card;
    }

    public LocalDateTime getClaimedAt() {
        return claimedAt;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public JsonNode getRewardData() {
        return rewardData;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserRewardClaim that = (UserRewardClaim) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserRewardClaim{" +
                "id=" + id +
                ", userId=" + userId +
                ", description='" + getRewardDescription() + '\'' +
                ", claimedAt=" + claimedAt +
                ", isActive=" + isActive() +
                '}';
    }
}
