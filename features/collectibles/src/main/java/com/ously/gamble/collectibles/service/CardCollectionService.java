package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.CardCollectionController.CreateCardCollectionRequest;
import com.ously.gamble.collectibles.controller.CardCollectionController.UpdateCardCollectionRequest;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CardCollectionService {

    private final CardCollectionRepository cardCollectionRepository;

    public CardCollectionService(CardCollectionRepository cardCollectionRepository) {
        this.cardCollectionRepository = cardCollectionRepository;
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findAll() {
        return cardCollectionRepository.findByOrderBySortOrderAscCreatedAtAsc();
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findAll(Pageable pageable) {
        return cardCollectionRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findById(Integer id) {
        return cardCollectionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithRewards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCardsAndRewards(id));
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findActiveCollections() {
        return cardCollectionRepository.findActiveCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiredCollections() {
        return cardCollectionRepository.findExpiredCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByName(String name) {
        return cardCollectionRepository.findByNameContainingIgnoreCase(name);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiringBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return cardCollectionRepository.findExpiringBetween(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public long countByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.countByStatus(status);
    }

    public CardCollection create(CreateCardCollectionRequest request) {
        CardCollection collection;

        if (request.startDate() != null && request.endDate() != null) {
            collection = CardCollection.createTimeLimitedCollection(
                request.name(), request.description(), request.startDate(), request.endDate());
        } else {
            collection = CardCollection.createCollection(request.name(), request.description());
            if (request.startDate() != null) {
                collection.changeStartDate(request.startDate());
            }
            if (request.endDate() != null) {
                collection.changeEndDate(request.endDate());
            }
        }

        if (request.sortOrder() != null) {
            collection.changeSortOrder(request.sortOrder());
        }

        return cardCollectionRepository.save(collection);
    }

    public Optional<CardCollection> update(Integer id, UpdateCardCollectionRequest request) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    if (request.name() != null) {
                        collection.rename(request.name());
                    }
                    if (request.description() != null) {
                        collection.changeDescription(request.description());
                    }
                    if (request.startDate() != null) {
                        collection.changeStartDate(request.startDate());
                    }
                    if (request.endDate() != null) {
                        collection.changeEndDate(request.endDate());
                    }
                    if (request.status() != null) {
                        switch (request.status()) {
                            case ENABLED -> collection.activate();
                            case DISABLED -> collection.disable();
                            case EXPIRED -> collection.expire();
                        }
                    }
                    if (request.sortOrder() != null) {
                        collection.changeSortOrder(request.sortOrder());
                    }
                    return cardCollectionRepository.save(collection);
                });
    }

    public boolean delete(Integer id) {
        if (cardCollectionRepository.existsById(id)) {
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Optional<CardCollection> activate(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.activate();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> disable(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.disable();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> expire(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.expire();
                    return cardCollectionRepository.save(collection);
                });
    }

    public void expireCollectionsEndingBefore(LocalDateTime dateTime) {
        List<CardCollection> expiring = cardCollectionRepository.findExpiringBetween(
                LocalDateTime.now().minusYears(10), dateTime);
        
        expiring.forEach(collection -> {
            if (!collection.isExpired()) {
                collection.expire();
            }
        });
        
        cardCollectionRepository.saveAll(expiring);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionActive(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isActive)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionExpired(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isExpired)
                .orElse(true);
    }

    // Aggregate Root methods for managing Cards
    public Integer createCard(Integer collectionId, String name, String imageUrl, Byte rarityLevel) {
        return cardCollectionRepository.findById(collectionId)
                .map(collection -> {
                    Integer cardId = collection.createCard(name, imageUrl, rarityLevel);
                    cardCollectionRepository.save(collection);
                    return cardId;
                })
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
    }

    public void updateCard(Integer collectionId, Integer cardId, CardCollection.UpdateCardRequest request) {
        cardCollectionRepository.findById(collectionId)
                .ifPresentOrElse(
                    collection -> {
                        collection.updateCard(cardId, request);
                        cardCollectionRepository.save(collection);
                    },
                    () -> { throw new IllegalArgumentException("Collection not found: " + collectionId); }
                );
    }

    public void deleteCard(Integer collectionId, Integer cardId) {
        cardCollectionRepository.findById(collectionId)
                .ifPresentOrElse(
                    collection -> {
                        collection.deleteCard(cardId);
                        cardCollectionRepository.save(collection);
                    },
                    () -> { throw new IllegalArgumentException("Collection not found: " + collectionId); }
                );
    }

    // Aggregate Root methods for managing Rewards
    public Integer createCompletionRewardForCollection(Integer collectionId, com.fasterxml.jackson.databind.JsonNode rewardData) {
        return cardCollectionRepository.findById(collectionId)
                .map(collection -> {
                    Integer rewardId = collection.createCompletionRewardForCollection(rewardData);
                    cardCollectionRepository.save(collection);
                    return rewardId;
                })
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
    }

    public Integer createMilestoneRewardForCollection(Integer collectionId, Byte percentage, com.fasterxml.jackson.databind.JsonNode rewardData) {
        return cardCollectionRepository.findById(collectionId)
                .map(collection -> {
                    Integer rewardId = collection.createMilestoneRewardForCollection(percentage, rewardData);
                    cardCollectionRepository.save(collection);
                    return rewardId;
                })
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
    }

    public Integer createCompletionRewardForCard(Integer collectionId, Integer cardId, com.fasterxml.jackson.databind.JsonNode rewardData) {
        return cardCollectionRepository.findById(collectionId)
                .map(collection -> {
                    Integer rewardId = collection.createCompletionRewardForCard(cardId, rewardData);
                    cardCollectionRepository.save(collection);
                    return rewardId;
                })
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
    }

    public Integer createMilestoneRewardForCard(Integer collectionId, Integer cardId, Byte percentage, com.fasterxml.jackson.databind.JsonNode rewardData) {
        return cardCollectionRepository.findById(collectionId)
                .map(collection -> {
                    Integer rewardId = collection.createMilestoneRewardForCard(cardId, percentage, rewardData);
                    cardCollectionRepository.save(collection);
                    return rewardId;
                })
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
    }

    public void updateReward(Integer collectionId, Integer rewardId, com.fasterxml.jackson.databind.JsonNode newRewardData, Byte newPercentage) {
        cardCollectionRepository.findById(collectionId)
                .ifPresentOrElse(
                    collection -> {
                        collection.updateReward(rewardId, newRewardData, newPercentage);
                        cardCollectionRepository.save(collection);
                    },
                    () -> { throw new IllegalArgumentException("Collection not found: " + collectionId); }
                );
    }

    public void deleteReward(Integer collectionId, Integer rewardId) {
        cardCollectionRepository.findById(collectionId)
                .ifPresentOrElse(
                    collection -> {
                        collection.deleteReward(rewardId);
                        cardCollectionRepository.save(collection);
                    },
                    () -> { throw new IllegalArgumentException("Collection not found: " + collectionId); }
                );
    }
}
