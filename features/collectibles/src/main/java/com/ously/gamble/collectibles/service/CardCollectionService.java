package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CardCollectionService {

    private final CardCollectionRepository cardCollectionRepository;

    public CardCollectionService(CardCollectionRepository cardCollectionRepository) {
        this.cardCollectionRepository = cardCollectionRepository;
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findAll() {
        return cardCollectionRepository.findByOrderBySortOrderAscCreatedAtAsc();
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findAll(Pageable pageable) {
        return cardCollectionRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findById(Integer id) {
        return cardCollectionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithRewards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCardsAndRewards(id));
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findActiveCollections() {
        return cardCollectionRepository.findActiveCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiredCollections() {
        return cardCollectionRepository.findExpiredCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByName(String name) {
        return cardCollectionRepository.findByNameContainingIgnoreCase(name);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiringBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return cardCollectionRepository.findExpiringBetween(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public long countByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.countByStatus(status);
    }

    public CardCollection create(CardCollectionDto.CreateCardCollectionRequest request) {
        CardCollection collection;

        if (request.startDate() != null && request.endDate() != null) {
            collection = CardCollection.createTimeLimitedCollection(
                request.name(), request.description(), request.startDate(), request.endDate());
        } else {
            collection = CardCollection.createCollection(request.name(), request.description());
            if (request.startDate() != null) {
                collection.changeStartDate(request.startDate());
            }
            if (request.endDate() != null) {
                collection.changeEndDate(request.endDate());
            }
        }

        if (request.sortOrder() != null) {
            collection.changeSortOrder(request.sortOrder());
        }

        return cardCollectionRepository.save(collection);
    }

    public Optional<CardCollection> update(Integer id, CardCollectionDto.UpdateCardCollectionRequest request) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    if (request.name() != null) {
                        collection.rename(request.name());
                    }
                    if (request.description() != null) {
                        collection.changeDescription(request.description());
                    }
                    if (request.startDate() != null) {
                        collection.changeStartDate(request.startDate());
                    }
                    if (request.endDate() != null) {
                        collection.changeEndDate(request.endDate());
                    }
                    if (request.status() != null) {
                        switch (request.status()) {
                            case ENABLED -> collection.activate();
                            case DISABLED -> collection.disable();
                            case EXPIRED -> collection.expire();
                        }
                    }
                    if (request.sortOrder() != null) {
                        collection.changeSortOrder(request.sortOrder());
                    }
                    return cardCollectionRepository.save(collection);
                });
    }

    public boolean delete(Integer id) {
        if (cardCollectionRepository.existsById(id)) {
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Optional<CardCollection> activate(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.activate();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> disable(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.disable();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> expire(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.expire();
                    return cardCollectionRepository.save(collection);
                });
    }

    public void expireCollectionsEndingBefore(LocalDateTime dateTime) {
        List<CardCollection> expiring = cardCollectionRepository.findExpiringBetween(
                LocalDateTime.now().minusYears(10), dateTime);
        
        expiring.forEach(collection -> {
            if (!collection.isExpired()) {
                collection.expire();
            }
        });
        
        cardCollectionRepository.saveAll(expiring);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionActive(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isActive)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionExpired(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isExpired)
                .orElse(true);
    }

    // DDD Aggregate Root methods for managing Cards
    public CardCollection createCard(Integer collectionId, CardCollectionDto.CreateCardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        collection.createCard(request.name(), request.imageUrl(), request.rarityLevel());
        return cardCollectionRepository.save(collection);
    }

    public CardCollection updateCard(Integer collectionId, Integer cardId, CardCollectionDto.UpdateCardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        // Преобразуем DTO в внутренний UpdateCardRequest
        CardCollection.CardRequest internalRequest = new CardCollection.CardRequest(
                request.name(),
                request.imageUrl(),
                request.rarityLevel(),
                request.startDate(),
                request.endDate(),
                request.status(),
                request.sortOrder()
        );

        collection.updateCard(cardId, internalRequest);
        return cardCollectionRepository.save(collection);
    }

    public CardCollection deleteCard(Integer collectionId, Integer cardId) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        collection.deleteCard(cardId);
        return cardCollectionRepository.save(collection);
    }

    // DDD Aggregate Root methods for managing Rewards
    public CardCollection createReward(Integer collectionId, CardCollectionDto.CreateRewardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        Reward.RewardType rewardType = mapRewardType(request.rewardType());

        if (request.cardId() != null) {
            // Награда для карты
            if (rewardType == Reward.RewardType.MILESTONE) {
                collection.createMilestoneRewardForCard(request.cardId(), request.milestonePercentage(), request.rewardData());
            } else {
                collection.createCompletionRewardForCard(request.cardId(), request.rewardData());
            }
        } else {
            // Награда для коллекции
            if (rewardType == Reward.RewardType.MILESTONE) {
                collection.createMilestoneRewardForCollection(request.milestonePercentage(), request.rewardData());
            } else {
                collection.createCompletionRewardForCollection(request.rewardData());
            }
        }

        return cardCollectionRepository.save(collection);
    }

    public CardCollection updateReward(Integer collectionId, Integer rewardId, CardCollectionDto.UpdateRewardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        collection.updateReward(rewardId, request.rewardData(), request.milestonePercentage());
        return cardCollectionRepository.save(collection);
    }

    public CardCollection deleteReward(Integer collectionId, Integer rewardId) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));

        collection.deleteReward(rewardId);
        return cardCollectionRepository.save(collection);
    }

    private Reward.RewardType mapRewardType(CardCollectionDto.CreateRewardRequest.RewardType rewardType) {
        return switch (rewardType) {
            case COMPLETION -> Reward.RewardType.COMPLETION;
            case MILESTONE -> Reward.RewardType.MILESTONE;
        };
    }
}
