package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.CardCollectionController.CreateCardCollectionRequest;
import com.ously.gamble.collectibles.controller.CardCollectionController.UpdateCardCollectionRequest;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CardCollectionService {

    private final CardCollectionRepository cardCollectionRepository;

    public CardCollectionService(CardCollectionRepository cardCollectionRepository) {
        this.cardCollectionRepository = cardCollectionRepository;
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findAll() {
        return cardCollectionRepository.findByOrderBySortOrderAscCreatedAtAsc();
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findAll(Pageable pageable) {
        return cardCollectionRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findById(Integer id) {
        return cardCollectionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithRewards(id));
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCardsAndRewards(id));
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findActiveCollections() {
        return cardCollectionRepository.findActiveCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiredCollections() {
        return cardCollectionRepository.findExpiredCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByName(String name) {
        return cardCollectionRepository.findByNameContainingIgnoreCase(name);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findExpiringBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return cardCollectionRepository.findExpiringBetween(startDate, endDate);
    }

    @Transactional(readOnly = true)
    public long countByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.countByStatus(status);
    }

    public CardCollection create(CreateCardCollectionRequest request) {
        CardCollection collection = new CardCollection(request.name(), request.description());
        
        if (request.startDate() != null) {
            collection.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            collection.setEndDate(request.endDate());
        }
        if (request.sortOrder() != null) {
            collection.setSortOrder(request.sortOrder());
        }
        
        return cardCollectionRepository.save(collection);
    }

    public Optional<CardCollection> update(Integer id, UpdateCardCollectionRequest request) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    if (request.name() != null) {
                        collection.setName(request.name());
                    }
                    if (request.description() != null) {
                        collection.setDescription(request.description());
                    }
                    if (request.startDate() != null) {
                        collection.setStartDate(request.startDate());
                    }
                    if (request.endDate() != null) {
                        collection.setEndDate(request.endDate());
                    }
                    if (request.status() != null) {
                        collection.setStatus(request.status());
                    }
                    if (request.sortOrder() != null) {
                        collection.setSortOrder(request.sortOrder());
                    }
                    return cardCollectionRepository.save(collection);
                });
    }

    public boolean delete(Integer id) {
        if (cardCollectionRepository.existsById(id)) {
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Optional<CardCollection> activate(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.activate();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> disable(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.disable();
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> expire(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.expire();
                    return cardCollectionRepository.save(collection);
                });
    }

    public void expireCollectionsEndingBefore(LocalDateTime dateTime) {
        List<CardCollection> expiring = cardCollectionRepository.findExpiringBetween(
                LocalDateTime.now().minusYears(10), dateTime);
        
        expiring.forEach(collection -> {
            if (!collection.isExpired()) {
                collection.expire();
            }
        });
        
        cardCollectionRepository.saveAll(expiring);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionActive(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isActive)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isCollectionExpired(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(CardCollection::isExpired)
                .orElse(true);
    }
}
