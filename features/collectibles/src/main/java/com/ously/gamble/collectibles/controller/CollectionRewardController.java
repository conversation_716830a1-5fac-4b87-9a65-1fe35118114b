package com.ously.gamble.collectibles.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.CollectionReward;
import com.ously.gamble.collectibles.service.CollectionRewardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/collection-rewards")
public class CollectionRewardController {

    private final CollectionRewardService collectionRewardService;

    public CollectionRewardController(CollectionRewardService collectionRewardService) {
        this.collectionRewardService = collectionRewardService;
    }

    @Operation(description = "Get all collection rewards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getAllRewards() {
        return collectionRewardService.findAll();
    }

    @Operation(description = "Get collection rewards with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<CollectionReward> getRewardsPaged(@PageableDefault(size = 20) Pageable pageable) {
        return collectionRewardService.findAll(pageable);
    }

    @Operation(description = "Get rewards by collection", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collection/{collectionId}")
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getRewardsByCollection(@PathVariable Integer collectionId) {
        return collectionRewardService.findByCollectionId(collectionId);
    }

    @Operation(description = "Get rewards by card", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/card/{cardId}")
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getRewardsByCard(@PathVariable Integer cardId) {
        return collectionRewardService.findByCardId(cardId);
    }

    @Operation(description = "Get completion rewards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/completion")
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getCompletionRewards() {
        return collectionRewardService.findCompletionRewards();
    }

    @Operation(description = "Get milestone rewards", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/milestone")
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getMilestoneRewards() {
        return collectionRewardService.findMilestoneRewards();
    }

    @Operation(description = "Get milestone rewards by percentage", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/milestone/{percentage}")
    @RolesAllowed("ADMIN")
    public List<CollectionReward> getMilestoneRewardsByPercentage(@PathVariable Byte percentage) {
        return collectionRewardService.findMilestoneRewardsByPercentage(percentage);
    }

    @Operation(description = "Get collection reward by ID", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CollectionReward> getRewardById(@PathVariable Integer id) {
        return collectionRewardService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create new collection reward", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CollectionReward createReward(@RequestBody CreateCollectionRewardRequest request) {
        return collectionRewardService.create(request);
    }

    @Operation(description = "Update collection reward", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CollectionReward> updateReward(
            @PathVariable Integer id,
            @RequestBody UpdateCollectionRewardRequest request) {
        return collectionRewardService.update(id, request)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Delete collection reward", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteReward(@PathVariable Integer id) {
        if (collectionRewardService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // DTOs
    public record CreateCollectionRewardRequest(
            Integer cardCollectionId,
            Integer cardId,
            CollectionReward.RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData
    ) {}

    public record UpdateCollectionRewardRequest(
            CollectionReward.RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData
    ) {}
}
