package com.ously.gamble.collectibles.persistence.model;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Entity
@Table(name = "card_collections")
public class CardCollection {
    public enum CollectionStatus {
        ENABLED, EXPIRED, DISABLED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate = LocalDateTime.now();

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CollectionStatus status = CollectionStatus.DISABLED;

    @Column(name = "sort_order")
    private Integer sortOrder = 99;

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Card> cards = new ArrayList<>();

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Reward> rewards = new ArrayList<>();

    // Constructors and factory methods
    protected CardCollection() {}

    public static CardCollection create(CardCollectionDto.CreateCardCollectionRequest request) {
        CardCollection collection = new CardCollection();
        collection.rename(request.name());
        collection.changeStartDate(request.startDate());
        collection.changeEndDate(request.endDate());
        return collection;
    }

    // Rich Domain Model methods
    public boolean isActive() {
        return status == CollectionStatus.ENABLED && 
               (endDate == null || endDate.isAfter(LocalDateTime.now())) &&
               startDate.isBefore(LocalDateTime.now());
    }

    public boolean isExpired() {
        return status == CollectionStatus.EXPIRED || 
               (endDate != null && endDate.isBefore(LocalDateTime.now()));
    }

    public boolean isDisabled() {
        return status == CollectionStatus.DISABLED;
    }

    public void activate() {
        if (isExpired()) {
            throw new IllegalStateException("Cannot activate expired collection");
        }

        if (cards.isEmpty()) {
            throw new IllegalStateException("Cannot activate empty collection");
        }

        this.status = CollectionStatus.ENABLED;
    }

    public void disable() {
        this.status = CollectionStatus.DISABLED;
    }

    public void expire() {
        this.status = CollectionStatus.EXPIRED;
    }

    public List<Card> getAllCards() {
        return cards;
    }

    public List<Card> getActiveCards() {
        return cards.stream()
                .filter(Card::isActive)
                .toList();
    }

    public List<Card> getDisabledCards() {
        return cards.stream()
                .filter(Card::isDisabled)
                .toList();
    }

    public List<Reward> getCompletionRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.COMPLETION)
                .toList();
    }

    public List<Reward> getMilestoneRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.MILESTONE)
                .toList();
    }

    // Aggregate Root methods for managing Cards
    public Integer createCard(CardCollectionDto.CreateCardRequest request) {
        Card card = Card.create(request);
        this.cards.add(card);
        return card.getId();
    }

    public void updateCard(Integer cardId, CardCollectionDto.UpdateCardRequest request) {
        Card card = findCardById(cardId);
        card.update(request);
    }

    public void deleteCard(Integer cardId) {
        Card card = findCardById(cardId);
        this.cards.remove(card);

        if (cards.isEmpty()) {
            this.disable();
        }
    }

    private Card findCardById(Integer cardId) {
        return cards.stream()
                .filter(card -> Objects.equals(card.getId(), cardId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Card not found with ID: " + cardId));
    }

    // Aggregate Root methods for managing Rewards
    public Integer createCompletionRewardForCollection(com.fasterxml.jackson.databind.JsonNode rewardData) {
        Reward reward = Reward.createCompletionRewardForCollection(this, rewardData);
        this.rewards.add(reward);
        return reward.getId();
    }

    public Integer createMilestoneRewardForCollection(Byte percentage, com.fasterxml.jackson.databind.JsonNode rewardData) {
        Reward reward = Reward.createMilestoneRewardForCollection(this, percentage, rewardData);
        this.rewards.add(reward);
        return reward.getId();
    }

    public Integer createCompletionRewardForCard(Integer cardId, com.fasterxml.jackson.databind.JsonNode rewardData) {
        Card card = findCardById(cardId);
        Reward reward = Reward.createCompletionRewardForCard(card, rewardData);
        this.rewards.add(reward);
        return reward.getId();
    }

    public Integer createMilestoneRewardForCard(Integer cardId, Byte percentage, com.fasterxml.jackson.databind.JsonNode rewardData) {
        Card card = findCardById(cardId);
        Reward reward = Reward.createMilestoneRewardForCard(card, percentage, rewardData);
        this.rewards.add(reward);
        return reward.getId();
    }

    public void updateReward(Integer rewardId, com.fasterxml.jackson.databind.JsonNode newRewardData, Byte newPercentage) {
        Reward reward = findRewardById(rewardId);

        if (newRewardData != null) {
            reward.changeRewardData(newRewardData);
        }

        if (newPercentage != null) {
            reward.changeMilestonePercentage(newPercentage);
        }
    }

    public void deleteReward(Integer rewardId) {
        Reward reward = findRewardById(rewardId);
        this.rewards.remove(reward);
    }

    private Reward findRewardById(Integer rewardId) {
        return rewards.stream()
                .filter(reward -> Objects.equals(reward.getId(), rewardId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Reward not found with ID: " + rewardId));
    }

    // Modifiers
    public void rename(String newName) {
        this.name = Objects.requireNonNull(newName, "Name cannot be null");
    }

    public void changeStartDate(LocalDateTime startDate) {
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        if (endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }
        this.startDate = startDate;
    }

    public void changeEndDate(LocalDateTime endDate) {
        if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("End date cannot be before start date");
        }
        this.endDate = endDate;
    }

    public void changeSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // Getters
    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public CollectionStatus getStatus() {
        return status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public List<Card> getCards() {
        return new ArrayList<>(cards);
    }

    public List<Reward> getRewards() {
        return new ArrayList<>(rewards);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardCollection that = (CardCollection) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "CardCollection{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
