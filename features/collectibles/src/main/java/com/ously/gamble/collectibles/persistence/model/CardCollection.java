package com.ously.gamble.collectibles.persistence.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "card_collections")
public class CardCollection {
    private enum CollectionStatus {
        ENABLED, EXPIRED, DISABLED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate = LocalDateTime.now();

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CollectionStatus status = CollectionStatus.DISABLED;

    @Column(name = "sort_order")
    private Integer sortOrder = 99;

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Card> cards = new ArrayList<>();

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Reward> rewards = new ArrayList<>();

    // Constructors and factory methods
    protected CardCollection() {}

    public static CardCollection createTimeLimitedCollection(String name,
                                                             LocalDateTime startDate,
                                                             LocalDateTime endDate) {
        CardCollection collection = new CardCollection();
        collection.rename(name);
        collection.changeStartDate(startDate);
        collection.changeEndDate(endDate);
        return collection;
    }

    // Rich Domain Model methods
    public boolean isActive() {
        return status == CollectionStatus.ENABLED && 
               (endDate == null || endDate.isAfter(LocalDateTime.now())) &&
               startDate.isBefore(LocalDateTime.now());
    }

    public boolean isExpired() {
        return status == CollectionStatus.EXPIRED || 
               (endDate != null && endDate.isBefore(LocalDateTime.now()));
    }

    public boolean isDisabled() {
        return status == CollectionStatus.DISABLED;
    }

    public void activate() {
        if (isExpired()) {
            throw new IllegalStateException("Cannot activate expired collection");
        }

        if (cards.isEmpty()) {
            throw new IllegalStateException("Cannot activate empty collection");
        }

        this.status = CollectionStatus.ENABLED;
    }

    public void disable() {
        this.status = CollectionStatus.DISABLED;
    }

    public void expire() {
        this.status = CollectionStatus.EXPIRED;
    }

    public List<Card> getAllCards() {
        return cards;
    }

    public List<Card> getActiveCards() {
        return cards.stream()
                .filter(Card::isActive)
                .toList();
    }

    public List<Card> getDisabledCards() {
        return cards.stream()
                .filter(Card::isDisabled)
                .toList();
    }

    public List<Reward> getCompletionRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.COMPLETION)
                .toList();
    }

    public List<Reward> getMilestoneRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.MILESTONE)
                .toList();
    }

    // Aggregate Root methods for managing Cards
    public Integer createCard(String name, String imageUrl, Byte rarityLevel) {
        validateCardCreation(name);

        Card card = switch (rarityLevel) {
            case 1 -> Card.createCommon(name, this, imageUrl);
            case 2 -> Card.createUncommon(name, this, imageUrl);
            case 3 -> Card.createRare(name, this, imageUrl);
            default -> throw new IllegalArgumentException("Invalid rarity level: " + rarityLevel);
        };

        this.cards.add(card);
        validateCardInvariants();
        return card.getId();
    }

    public void updateCard(Integer cardId, UpdateCardRequest request) {
        Card card = findCardById(cardId);

        if (request.name() != null) {
            validateCardNameUniqueness(request.name(), cardId);
            card.rename(request.name());
        }

        if (request.imageUrl() != null) {
            card.changeImage(request.imageUrl());
        }

        if (request.rarityLevel() != null) {
            switch (request.rarityLevel()) {
                case 1 -> card.changeRarityToCommon();
                case 2 -> card.changeRarityToUncommon();
                case 3 -> card.changeRarityToRare();
                default -> throw new IllegalArgumentException("Invalid rarity level: " + request.rarityLevel());
            }
        }

        if (request.startDate() != null) {
            card.changeStartDate(request.startDate());
        }

        if (request.endDate() != null) {
            card.changeEndDate(request.endDate());
        }

        if (request.status() != null) {
            switch (request.status()) {
                case ENABLED -> card.activate();
                case DISABLED -> card.disable();
                case EXPIRED -> card.expire();
            }
        }

        if (request.sortOrder() != null) {
            card.changeSortOrder(request.sortOrder());
        }

        validateCardInvariants();
    }

    public void deleteCard(Integer cardId) {
        Card card = findCardById(cardId);
        validateCardDeletion(card);
        this.cards.remove(card);
    }

    private Card findCardById(Integer cardId) {
        return cards.stream()
                .filter(card -> Objects.equals(card.getId(), cardId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Card not found with ID: " + cardId));
    }

    // Modifiers
    public void rename(String newName) {
        this.name = Objects.requireNonNull(newName, "Name cannot be null");
    }

    public void changeStartDate(LocalDateTime startDate) {
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        if (endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }
        this.startDate = startDate;
    }

    public void changeEndDate(LocalDateTime endDate) {
        if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("End date cannot be before start date");
        }
        this.endDate = endDate;
    }

    public void changeSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // Getters
    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public CollectionStatus getStatus() {
        return status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public List<Card> getCards() {
        return new ArrayList<>(cards);
    }

    public List<Reward> getRewards() {
        return new ArrayList<>(rewards);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardCollection that = (CardCollection) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "CardCollection{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
