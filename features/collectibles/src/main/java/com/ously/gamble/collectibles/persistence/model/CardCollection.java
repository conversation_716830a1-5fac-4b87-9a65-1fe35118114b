package com.ously.gamble.collectibles.persistence.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "card_collections")
public class CardCollection {
    private enum CollectionStatus {
        ENABLED, EXPIRED, DISABLED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate = LocalDateTime.now();

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CollectionStatus status = CollectionStatus.DISABLED;

    @Column(name = "sort_order")
    private Integer sortOrder = 99;

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Card> cards = new ArrayList<>();

    @OneToMany(mappedBy = "cardCollection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Reward> rewards = new ArrayList<>();

    // Constructors and factory methods
    protected CardCollection() {}

    public static CardCollection createTimeLimitedCollection(String name,
                                                             LocalDateTime startDate,
                                                             LocalDateTime endDate) {
        CardCollection collection = new CardCollection();
        collection.rename(name);
        collection.changeStartDate(startDate);
        collection.changeEndDate(endDate);
        return collection;
    }

    // Rich Domain Model methods
    public boolean isActive() {
        return status == CollectionStatus.ENABLED && 
               (endDate == null || endDate.isAfter(LocalDateTime.now())) &&
               startDate.isBefore(LocalDateTime.now());
    }

    public boolean isExpired() {
        return status == CollectionStatus.EXPIRED || 
               (endDate != null && endDate.isBefore(LocalDateTime.now()));
    }

    public boolean isDisabled() {
        return status == CollectionStatus.DISABLED;
    }

    public void activate() {
        if (isExpired()) {
            throw new IllegalStateException("Cannot activate expired collection");
        }

        if (cards.isEmpty()) {
            throw new IllegalStateException("Cannot activate empty collection");
        }

        this.status = CollectionStatus.ENABLED;
    }

    public void disable() {
        this.status = CollectionStatus.DISABLED;
    }

    public void expire() {
        this.status = CollectionStatus.EXPIRED;
    }

    public List<Card> getAllCards() {
        return cards;
    }

    public List<Card> getActiveCards() {
        return cards.stream()
                .filter(Card::isActive)
                .toList();
    }

    public List<Card> getDisabledCards() {
        return cards.stream()
                .filter(Card::isDisabled)
                .toList();
    }

    public List<Reward> getCompletionRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.COMPLETION)
                .toList();
    }

    public List<Reward> getMilestoneRewards() {
        return rewards.stream()
                .filter(reward -> reward.getRewardType() == Reward.RewardType.MILESTONE)
                .toList();
    }

    // Aggregate Root methods for managing Cards
    public Card addCommonCard(String name, String imageUrl) {
        Card card = Card.createCommon(name, this, imageUrl);
        this.cards.add(card);
        return card;
    }

    public Card addUncommonCard(String name, String imageUrl) {
        Card card = Card.createUncommon(name, this, imageUrl);
        this.cards.add(card);
        return card;
    }

    public Card addRareCard(String name, String imageUrl) {
        Card card = Card.createRare(name, this, imageUrl);
        this.cards.add(card);
        return card;
    }

    public void removeCard(Card card) {
        this.cards.remove(card);
    }

    // Modifiers
    public void rename(String newName) {
        this.name = Objects.requireNonNull(newName, "Name cannot be null");
    }

    public void changeStartDate(LocalDateTime startDate) {
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        if (endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }
        this.startDate = startDate;
    }

    public void changeEndDate(LocalDateTime endDate) {
        if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("End date cannot be before start date");
        }
        this.endDate = endDate;
    }

    public void changeSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // Getters
    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public CollectionStatus getStatus() {
        return status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public List<Card> getCards() {
        return new ArrayList<>(cards);
    }

    public List<Reward> getRewards() {
        return new ArrayList<>(rewards);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardCollection that = (CardCollection) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "CardCollection{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status=" + status +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
