//package com.ously.gamble.collectibles.persistence.repository;
//
//import com.ously.gamble.collectibles.persistence.model.CollectionReward;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public interface CollectionRewardRepository extends JpaRepository<CollectionReward, Integer> {
//
//    List<CollectionReward> findByCardCollectionId(Integer cardCollectionId);
//
//    List<CollectionReward> findByCardId(Integer cardId);
//
//    List<CollectionReward> findByRewardType(CollectionReward.RewardType rewardType);
//
//    List<CollectionReward> findByRewardTypeAndMilestonePercentage(CollectionReward.RewardType rewardType,
//                                                                 Byte milestonePercentage);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.rewardType = 'COMPLETION'")
//    List<CollectionReward> findCompletionRewards();
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.rewardType = 'MILESTONE'")
//    List<CollectionReward> findMilestoneRewards();
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.rewardType = 'MILESTONE' " +
//           "AND cr.milestonePercentage = :percentage")
//    List<CollectionReward> findMilestoneRewardsByPercentage(@Param("percentage") Byte percentage);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.cardCollection.id = :collectionId " +
//           "AND cr.rewardType = 'COMPLETION'")
//    List<CollectionReward> findCompletionRewardsByCollection(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.cardCollection.id = :collectionId " +
//           "AND cr.rewardType = 'MILESTONE'")
//    List<CollectionReward> findMilestoneRewardsByCollection(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.card.id = :cardId " +
//           "AND cr.rewardType = 'COMPLETION'")
//    List<CollectionReward> findCompletionRewardsByCard(@Param("cardId") Integer cardId);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.card.id = :cardId " +
//           "AND cr.rewardType = 'MILESTONE'")
//    List<CollectionReward> findMilestoneRewardsByCard(@Param("cardId") Integer cardId);
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.cardCollection IS NOT NULL")
//    List<CollectionReward> findCollectionRewards();
//
//    @Query("SELECT cr FROM CollectionReward cr WHERE cr.card IS NOT NULL")
//    List<CollectionReward> findCardRewards();
//
//    @Query("SELECT COUNT(cr) FROM CollectionReward cr WHERE cr.cardCollection.id = :collectionId")
//    long countByCollectionId(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT COUNT(cr) FROM CollectionReward cr WHERE cr.card.id = :cardId")
//    long countByCardId(@Param("cardId") Integer cardId);
//
//    @Query("SELECT cr FROM CollectionReward cr " +
//           "LEFT JOIN FETCH cr.cardCollection cc " +
//           "LEFT JOIN FETCH cr.card c " +
//           "WHERE cr.id = :id")
//    CollectionReward findByIdWithCollectionAndCard(@Param("id") Integer id);
//
//    @Query("SELECT DISTINCT cr.milestonePercentage FROM CollectionReward cr " +
//           "WHERE cr.rewardType = 'MILESTONE' AND cr.milestonePercentage IS NOT NULL " +
//           "ORDER BY cr.milestonePercentage ASC")
//    List<Byte> findDistinctMilestonePercentages();
//}
