package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.Reward;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RewardRepository extends JpaRepository<Reward, Integer> {

    List<Reward> findByCardCollectionId(Integer cardCollectionId);

    List<Reward> findByCardId(Integer cardId);

    List<Reward> findByRewardType(Reward.RewardType rewardType);

    List<Reward> findByRewardTypeAndMilestonePercentage(Reward.RewardType rewardType, 
                                                                 Byte milestonePercentage);

    @Query("SELECT cr FROM Reward cr WHERE cr.rewardType = 'COMPLETION'")
    List<Reward> findCompletionRewards();

    @Query("SELECT cr FROM Reward cr WHERE cr.rewardType = 'MILESTONE'")
    List<Reward> findMilestoneRewards();

    @Query("SELECT cr FROM Reward cr WHERE cr.rewardType = 'MILESTONE' " +
           "AND cr.milestonePercentage = :percentage")
    List<Reward> findMilestoneRewardsByPercentage(@Param("percentage") Byte percentage);

    @Query("SELECT cr FROM Reward cr WHERE cr.cardCollection.id = :collectionId " +
           "AND cr.rewardType = 'COMPLETION'")
    List<Reward> findCompletionRewardsByCollection(@Param("collectionId") Integer collectionId);

    @Query("SELECT cr FROM Reward cr WHERE cr.cardCollection.id = :collectionId " +
           "AND cr.rewardType = 'MILESTONE'")
    List<Reward> findMilestoneRewardsByCollection(@Param("collectionId") Integer collectionId);

    @Query("SELECT cr FROM Reward cr WHERE cr.card.id = :cardId " +
           "AND cr.rewardType = 'COMPLETION'")
    List<Reward> findCompletionRewardsByCard(@Param("cardId") Integer cardId);

    @Query("SELECT cr FROM Reward cr WHERE cr.card.id = :cardId " +
           "AND cr.rewardType = 'MILESTONE'")
    List<Reward> findMilestoneRewardsByCard(@Param("cardId") Integer cardId);

    @Query("SELECT cr FROM Reward cr WHERE cr.cardCollection IS NOT NULL")
    List<Reward> findCollectionRewards();

    @Query("SELECT cr FROM Reward cr WHERE cr.card IS NOT NULL")
    List<Reward> findCardRewards();

    @Query("SELECT COUNT(cr) FROM Reward cr WHERE cr.cardCollection.id = :collectionId")
    long countByCollectionId(@Param("collectionId") Integer collectionId);

    @Query("SELECT COUNT(cr) FROM Reward cr WHERE cr.card.id = :cardId")
    long countByCardId(@Param("cardId") Integer cardId);

    @Query("SELECT cr FROM Reward cr " +
           "LEFT JOIN FETCH cr.cardCollection cc " +
           "LEFT JOIN FETCH cr.card c " +
           "WHERE cr.id = :id")
    Reward findByIdWithCollectionAndCard(@Param("id") Integer id);

    @Query("SELECT DISTINCT cr.milestonePercentage FROM Reward cr " +
           "WHERE cr.rewardType = 'MILESTONE' AND cr.milestonePercentage IS NOT NULL " +
           "ORDER BY cr.milestonePercentage ASC")
    List<Byte> findDistinctMilestonePercentages();
}
