package com.ously.gamble.collectibles.persistence.model;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "cards")
public class Card {
    public enum CardStatus {
        ENABLED, EXPIRED, DISABLED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_collection_id", nullable = false)
    private CardCollection cardCollection;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate = LocalDateTime.now();

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CardStatus status = CardStatus.DISABLED;

    @Column(name = "image_url", nullable = false)
    private String imageUrl;

    @Column(name = "rarity_level", nullable = false)
    private Byte rarityLevel;

    @Column(name = "sort_order")
    private Byte sortOrder = 99;

    @OneToMany(mappedBy = "card", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Reward> rewards = new ArrayList<>();

    // Constructors and factory methods
    protected Card() {
    }

    public static Card create(CardCollectionDto.CreateCardRequest request) {
        Card card = new Card();
        card.rename(request.name());
        card.changeImage(request.imageUrl());
        card.changeStartDate(request.startDate());
        card.changeEndDate(request.endDate());
        card.changeSortOrder(request.sortOrder());
        card.changeRarity(request.rarityLevel());
        return card;
    }

    // Rich Domain Model methods
    public void update(CardCollectionDto.UpdateCardRequest request) {
        if (request.name() != null) {
            rename(request.name());
        }

        if (request.imageUrl() != null) {
            changeImage(request.imageUrl());
        }

        if (request.rarityLevel() != null) {
            rarityLevel = request.rarityLevel();
        }

        if (request.startDate() != null) {
            changeStartDate(request.startDate());
        }

        if (request.endDate() != null) {
            changeEndDate(request.endDate());
        }

        if (request.status() != null) {
            switch (request.status()) {
                case ENABLED -> activate();
                case DISABLED -> disable();
                case EXPIRED -> expire();
            }
        }

        if (request.sortOrder() != null) {
            changeSortOrder(request.sortOrder());
        }
    }

    public boolean isActive() {
        return status == CardStatus.ENABLED &&
                cardCollection.isActive() &&
                (endDate == null || endDate.isAfter(LocalDateTime.now())) &&
                startDate.isBefore(LocalDateTime.now());
    }

    public boolean isExpired() {
        return status == CardStatus.EXPIRED ||
                (endDate != null && endDate.isBefore(LocalDateTime.now()));
    }

    public boolean isDisabled() {
        return status == CardStatus.DISABLED;
    }

    public void activate() throws IllegalStateException {
        if (isExpired()) {
            throw new IllegalStateException("Cannot activate expired card");
        }
        this.status = CardStatus.ENABLED;
    }

    public void disable() {
        this.status = CardStatus.DISABLED;
    }

    public void expire() {
        this.status = CardStatus.EXPIRED;
    }

    public boolean isRare() {
        return rarityLevel != null && rarityLevel == 3;
    }

    public boolean isUncommon() {
        return rarityLevel != null && rarityLevel == 2;
    }

    public boolean isCommon() {
        return rarityLevel != null && rarityLevel == 1;
    }

    // Modifiers
    public void rename(String newName) {
        this.name = Objects.requireNonNull(newName);
    }

    public void changeImage(String imageUrl) {
        this.imageUrl = Objects.requireNonNull(imageUrl);
    }

    public void changeRarity(Byte rarityLevel) {
        this.rarityLevel = rarityLevel;
    }

    public void changeStartDate(LocalDateTime startDate) {
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }

        if (endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }

        this.startDate = startDate;
    }

    public void changeEndDate(LocalDateTime endDate) {
        if (endDate == null) {
            throw new IllegalArgumentException("End date cannot be null");
        }

        if (startDate != null && endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("End date cannot be before start date");
        }
        this.endDate = endDate;
    }

    public void changeSortOrder(Byte sortOrder) {
        this.sortOrder = sortOrder;
    }

    public void replaceRewards(List<Reward> rewards) {
        this.rewards.clear();
        if (rewards != null) {
            this.rewards.addAll(rewards);
        }
    }

    // Getters
    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public Byte getSortOrder() {
        return sortOrder;
    }

    public CardCollection getCardCollection() {
        return cardCollection;
    }

    public List<Reward> getRewards() {
        return rewards;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Card card = (Card) o;
        return Objects.equals(id, card.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Card{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", status=" + status +
                ", imageUrl='" + imageUrl + '\'' +
                ", rarityLevel=" + rarityLevel +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
