//package com.ously.gamble.collectibles.persistence.repository;
//
//import com.ously.gamble.collectibles.persistence.model.UserRewardClaim;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//import org.springframework.stereotype.Repository;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//@Repository
//public interface UserRewardClaimRepository extends JpaRepository<UserRewardClaim, Long> {
//
//    List<UserRewardClaim> findByUserId(Long userId);
//
//    List<UserRewardClaim> findByCollectionRewardId(Integer collectionRewardId);
//
//    List<UserRewardClaim> findByCardCollectionId(Integer cardCollectionId);
//
//    List<UserRewardClaim> findByCardId(Integer cardId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.deletedAt IS NULL")
//    List<UserRewardClaim> findActiveClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.deletedAt IS NOT NULL")
//    List<UserRewardClaim> findDeletedClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.userId = :userId AND urc.deletedAt IS NULL")
//    List<UserRewardClaim> findActiveClaimsByUserId(@Param("userId") Long userId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.userId = :userId AND urc.deletedAt IS NOT NULL")
//    List<UserRewardClaim> findDeletedClaimsByUserId(@Param("userId") Long userId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.cardCollectionId = :collectionId AND urc.deletedAt IS NULL")
//    List<UserRewardClaim> findActiveClaimsByCollectionId(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.cardId = :cardId AND urc.deletedAt IS NULL")
//    List<UserRewardClaim> findActiveClaimsByCardId(@Param("cardId") Integer cardId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.collectionReward.rewardType = 'COMPLETION'")
//    List<UserRewardClaim> findCompletionClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.collectionReward.rewardType = 'MILESTONE'")
//    List<UserRewardClaim> findMilestoneClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.collectionReward.cardCollection IS NOT NULL")
//    List<UserRewardClaim> findCollectionRewardClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.collectionReward.card IS NOT NULL")
//    List<UserRewardClaim> findCardRewardClaims();
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.claimedAt BETWEEN :startDate AND :endDate")
//    List<UserRewardClaim> findByClaimedAtBetween(@Param("startDate") LocalDateTime startDate,
//                                               @Param("endDate") LocalDateTime endDate);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.userId = :userId " +
//           "AND urc.claimedAt BETWEEN :startDate AND :endDate")
//    List<UserRewardClaim> findByUserIdAndClaimedAtBetween(@Param("userId") Long userId,
//                                                        @Param("startDate") LocalDateTime startDate,
//                                                        @Param("endDate") LocalDateTime endDate);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.deletedAt BETWEEN :startDate AND :endDate")
//    List<UserRewardClaim> findByDeletedAtBetween(@Param("startDate") LocalDateTime startDate,
//                                               @Param("endDate") LocalDateTime endDate);
//
//    @Query("SELECT COUNT(urc) FROM UserRewardClaim urc WHERE urc.userId = :userId AND urc.deletedAt IS NULL")
//    long countActiveClaimsByUserId(@Param("userId") Long userId);
//
//    @Query("SELECT COUNT(urc) FROM UserRewardClaim urc WHERE urc.cardCollectionId = :collectionId AND urc.deletedAt IS NULL")
//    long countActiveClaimsByCollectionId(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT COUNT(urc) FROM UserRewardClaim urc WHERE urc.cardId = :cardId AND urc.deletedAt IS NULL")
//    long countActiveClaimsByCardId(@Param("cardId") Integer cardId);
//
//    @Query("SELECT COUNT(urc) FROM UserRewardClaim urc WHERE urc.collectionRewardId = :rewardId AND urc.deletedAt IS NULL")
//    long countActiveClaimsByRewardId(@Param("rewardId") Integer rewardId);
//
//    @Query("SELECT COUNT(DISTINCT urc.userId) FROM UserRewardClaim urc WHERE urc.deletedAt IS NULL")
//    long countDistinctActiveUsers();
//
//    @Query("SELECT COUNT(DISTINCT urc.userId) FROM UserRewardClaim urc WHERE urc.cardCollectionId = :collectionId AND urc.deletedAt IS NULL")
//    long countDistinctActiveUsersByCollectionId(@Param("collectionId") Integer collectionId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc " +
//           "LEFT JOIN FETCH urc.collectionReward cr " +
//           "LEFT JOIN FETCH urc.cardCollection cc " +
//           "LEFT JOIN FETCH urc.card c " +
//           "WHERE urc.id = :id")
//    UserRewardClaim findByIdWithDetails(@Param("id") Long id);
//
//    boolean existsByUserIdAndCollectionRewardIdAndCardCollectionIdAndCardIdAndDeletedAtIsNull(
//            Long userId, Integer collectionRewardId, Integer cardCollectionId, Integer cardId);
//
//    @Query("SELECT urc FROM UserRewardClaim urc WHERE urc.userId = :userId " +
//           "AND urc.collectionRewardId = :rewardId " +
//           "AND urc.cardCollectionId = :collectionId " +
//           "AND (:cardId IS NULL OR urc.cardId = :cardId) " +
//           "AND urc.deletedAt IS NULL")
//    List<UserRewardClaim> findExistingClaim(@Param("userId") Long userId,
//                                          @Param("rewardId") Integer rewardId,
//                                          @Param("collectionId") Integer collectionId,
//                                          @Param("cardId") Integer cardId);
//}
