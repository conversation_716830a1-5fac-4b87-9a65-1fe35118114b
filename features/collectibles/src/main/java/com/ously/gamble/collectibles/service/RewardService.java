package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.controller.RewardController.CreateRewardRequest;
import com.ously.gamble.collectibles.controller.RewardController.UpdateRewardRequest;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.collectibles.persistence.repository.RewardRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class RewardService {

    private final RewardRepository rewardRepository;
    private final CardCollectionRepository cardCollectionRepository;
    private final CardRepository cardRepository;

    public RewardService(RewardRepository rewardRepository,
                                 CardCollectionRepository cardCollectionRepository,
                                 CardRepository cardRepository) {
        this.rewardRepository = rewardRepository;
        this.cardCollectionRepository = cardCollectionRepository;
        this.cardRepository = cardRepository;
    }

    @Transactional(readOnly = true)
    public List<Reward> findAll() {
        return rewardRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<Reward> findAll(Pageable pageable) {
        return rewardRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<Reward> findById(Integer id) {
        return rewardRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<Reward> findByIdWithDetails(Integer id) {
        return Optional.ofNullable(rewardRepository.findByIdWithCollectionAndCard(id));
    }

    @Transactional(readOnly = true)
    public List<Reward> findByCollectionId(Integer collectionId) {
        return rewardRepository.findByCardCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findByCardId(Integer cardId) {
        return rewardRepository.findByCardId(cardId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findCompletionRewards() {
        return rewardRepository.findCompletionRewards();
    }

    @Transactional(readOnly = true)
    public List<Reward> findMilestoneRewards() {
        return rewardRepository.findMilestoneRewards();
    }

    @Transactional(readOnly = true)
    public List<Reward> findMilestoneRewardsByPercentage(Byte percentage) {
        return rewardRepository.findMilestoneRewardsByPercentage(percentage);
    }

    @Transactional(readOnly = true)
    public List<Reward> findCompletionRewardsByCollection(Integer collectionId) {
        return rewardRepository.findCompletionRewardsByCollection(collectionId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findMilestoneRewardsByCollection(Integer collectionId) {
        return rewardRepository.findMilestoneRewardsByCollection(collectionId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findCompletionRewardsByCard(Integer cardId) {
        return rewardRepository.findCompletionRewardsByCard(cardId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findMilestoneRewardsByCard(Integer cardId) {
        return rewardRepository.findMilestoneRewardsByCard(cardId);
    }

    @Transactional(readOnly = true)
    public List<Reward> findCollectionRewards() {
        return rewardRepository.findCollectionRewards();
    }

    @Transactional(readOnly = true)
    public List<Reward> findCardRewards() {
        return rewardRepository.findCardRewards();
    }

    @Transactional(readOnly = true)
    public List<Byte> findDistinctMilestonePercentages() {
        return rewardRepository.findDistinctMilestonePercentages();
    }

    @Transactional(readOnly = true)
    public long countByCollectionId(Integer collectionId) {
        return rewardRepository.countByCollectionId(collectionId);
    }

    @Transactional(readOnly = true)
    public long countByCardId(Integer cardId) {
        return rewardRepository.countByCardId(cardId);
    }

    public Reward create(CreateRewardRequest request) {
        validateCreateRequest(request);

        Reward reward;
        
        if (request.cardCollectionId() != null) {
            CardCollection collection = cardCollectionRepository.findById(request.cardCollectionId())
                    .orElseThrow(() -> new IllegalArgumentException("Card collection not found: " + request.cardCollectionId()));
            
            if (request.rewardType() == Reward.RewardType.MILESTONE) {
                reward = Reward.createMilestoneRewardForCollection(
                    collection, request.milestonePercentage(), request.rewardData());
            } else {
                reward = Reward.createCompletionRewardForCollection(collection, request.rewardData());
            }
        } else {
            Card card = cardRepository.findById(request.cardId())
                    .orElseThrow(() -> new IllegalArgumentException("Card not found: " + request.cardId()));
            
            if (request.rewardType() == Reward.RewardType.MILESTONE) {
                reward = Reward.createMilestoneRewardForCard(
                    card, request.milestonePercentage(), request.rewardData());
            } else {
                reward = Reward.createCompletionRewardForCard(card, request.rewardData());
            }
        }
        
        return rewardRepository.save(reward);
    }

    public Optional<Reward> update(Integer id, UpdateRewardRequest request) {
        return rewardRepository.findById(id)
                .map(reward -> {
                    if (request.milestonePercentage() != null) {
                        reward.changeMilestonePercentage(request.milestonePercentage());
                    }
                    if (request.rewardData() != null) {
                        reward.changeRewardData(request.rewardData());
                    }
                    return rewardRepository.save(reward);
                });
    }

    public boolean delete(Integer id) {
        if (rewardRepository.existsById(id)) {
            rewardRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private void validateCreateRequest(CreateRewardRequest request) {
        if (request.cardCollectionId() == null && request.cardId() == null) {
            throw new IllegalArgumentException("Either cardCollectionId or cardId must be provided");
        }
        if (request.cardCollectionId() != null && request.cardId() != null) {
            throw new IllegalArgumentException("Cannot specify both cardCollectionId and cardId");
        }
        if (request.rewardType() == Reward.RewardType.MILESTONE && request.milestonePercentage() == null) {
            throw new IllegalArgumentException("Milestone percentage is required for milestone rewards");
        }
        if (request.rewardType() == Reward.RewardType.COMPLETION && request.milestonePercentage() != null) {
            throw new IllegalArgumentException("Milestone percentage should not be provided for completion rewards");
        }
        if (request.rewardData() == null) {
            throw new IllegalArgumentException("Reward data is required");
        }
    }

    @Transactional(readOnly = true)
    public boolean isRewardForCollection(Integer id) {
        return rewardRepository.findById(id)
                .map(Reward::isForCollection)
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public boolean isRewardForCard(Integer id) {
        return rewardRepository.findById(id)
                .map(Reward::isForCard)
                .orElse(false);
    }
}
