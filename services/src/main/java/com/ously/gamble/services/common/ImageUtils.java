package com.ously.gamble.services.common;

import ar.com.hjg.pngj.PngReader;
import ar.com.hjg.pngj.PngWriterHc;
import ar.com.hjg.pngj.chunks.ChunkCopyBehaviour;
import com.luciad.imageio.webp.WebPWriteParam;
import com.ously.gamble.persistence.model.game.GameImage;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.MemoryCacheImageOutputStream;
import java.awt.*;
import java.awt.RenderingHints.Key;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public final class ImageUtils {
    private ImageUtils() {
    }

    public static byte[] convertToPNG(BufferedImage nimg) throws IOException {

        var bout = new ByteArrayOutputStream();
        ImageIO.write(nimg, "png", bout);
        var data = bout.toByteArray();
        // Opt. PNG
        var rdr = new PngReader(new ByteArrayInputStream(data));
        var bOut = new ByteArrayOutputStream(64 * 1024);
        var wrt = new PngWriterHc(bOut, rdr.imgInfo);
        wrt.copyChunksFrom(rdr.getChunksList(), ChunkCopyBehaviour.COPY_ALL);
        for (var row = 0; row < rdr.imgInfo.rows; row++) {
            var l1 = rdr.readRow();
            wrt.writeRow(l1);
        }
        rdr.end();
        wrt.end();
        return bOut.toByteArray();
    }

    public static byte[] convertToWEBP(BufferedImage nimg, boolean lossless,
                                       float quality) throws IOException {
        var writer = ImageIO.getImageWritersByMIMEType("image/webp").next();

        var writeParam = new WebPWriteParam(writer.getLocale());
        writeParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        writeParam.setCompressionType(writeParam.getCompressionTypes()[WebPWriteParam.LOSSLESS_COMPRESSION]);

        if (!lossless) {
            writeParam = new WebPWriteParam(writer.getLocale());
            writeParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            writeParam.setCompressionType(writeParam.getCompressionTypes()[WebPWriteParam.LOSSY_COMPRESSION]);
            writeParam.setCompressionQuality(quality);
        }

        return getBytesForImage(writer, nimg, writeParam);
    }

    public static byte[] convertToJPEG(BufferedImage nimg, boolean lossless,
                                       float quality) throws IOException {

        var bufferedImage = new BufferedImage(nimg.getWidth(), nimg.getHeight(), BufferedImage.TYPE_3BYTE_BGR);
        bufferedImage.createGraphics().drawImage(nimg, 0, 0, null);


        var writers = ImageIO.getImageWritersByFormatName("jpg");
        if (!writers.hasNext()) {
            throw new IllegalStateException("No writers found");
        }

        var writer = writers.next();

        var writeParam = writer.getDefaultWriteParam();
        writeParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        writeParam.setCompressionQuality(quality);

        return getBytesForImage(writer, bufferedImage, writeParam);
    }

    private static byte[] getBytesForImage(ImageWriter writer, BufferedImage bufferedImage,
                                           ImageWriteParam writeParam) throws IOException {
        var bos = new ByteArrayOutputStream(64 * 1024);
        var mcios = new MemoryCacheImageOutputStream(bos);
        writer.setOutput(mcios);
        writer.write(null, new IIOImage(bufferedImage, null, null), writeParam);
        mcios.flush();
        return bos.toByteArray();
    }

    public static byte[] downsample(int cubewidth, byte[] image, boolean lossless,
                                    float quality) throws IOException {
        return downsample(cubewidth, cubewidth, image, null, lossless, quality);
    }

    public static byte[] downsample(int maxWidth, int maxHeight, byte[] image, GameImage gImg,
                                    boolean lossless, float quality) throws IOException {

        var oimg = ImageIO.read(new ByteArrayInputStream(image));
        var nimg = new BufferedImage(maxWidth, maxHeight, BufferedImage.TYPE_4BYTE_ABGR);

        var x = 0;
        var y = 0;
        var width = maxWidth;
        var height = maxHeight;
        float oHeight = oimg.getHeight();
        float oWidth = oimg.getWidth();

        // hoeher als breit
        if (oHeight >= oWidth) {
            var scale = 1 / (oHeight / maxHeight);
            x = (int) ((maxWidth - (oWidth * scale)) / 2);
            width = (int) (oWidth * scale);

        } else {
            var scale = 1 / (oWidth / maxWidth);
            y = (int) ((maxHeight - (oHeight * scale)) / 2);
            height = (int) (oHeight * scale);
        }

        // set quality parameters.

        var graphics = nimg.createGraphics();

        // Set high quality upon image downscaling
        graphics.addRenderingHints(getHints());

        var bgColor = new Color(0, 0, 0, 255);
        byte[] data = null;
        if (graphics.drawImage(oimg, x, y, width, height, bgColor, null)) {
            data = convertToWEBP(nimg, lossless, quality);
            if (gImg != null) {
                gImg.setImage(data);
                gImg.setRealFormat("" + oimg.getWidth() + 'x' + oimg.getHeight());
                gImg.setMimeType("image/webp");
            } else {
                return data;
            }
        }
        return data;
    }

    private static Map<Key, Object> getHints() {
        Map<Key, Object> hints = new HashMap<>();
        hints.put(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        hints.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        hints.put(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        hints.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        return hints;
    }


}
