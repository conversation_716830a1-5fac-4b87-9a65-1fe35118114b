package com.ously.gamble.services.notification;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.crm.FcmTokenService;
import com.ously.gamble.api.events.UserEventGateway;
import com.ously.gamble.api.notification.ContactRequest;
import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationService;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.api.user.removal.UserCleanupEvent;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.exception.RequestThrottledException;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.NotificationType;
import com.ously.gamble.persistence.repository.user.UserLoginsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;


/**
 * Prepare/Enrich a message with user,destination,... and offload into queue
 */
@Service
@ConditionalOnNotMonitor
public class NotificationServiceImpl implements NotificationService {
    private final Logger log = LoggerFactory.getLogger(NotificationServiceImpl.class);

    private final UserManagementService uMgm;
    private final UserLoginsRepository userLoginRepo;
    private final UserEventGateway userEventGateway;
    private final CachedMap<String, String> lockCache;
    private final FcmTokenService fcmTokenService;

    @Autowired
    public NotificationServiceImpl(UserManagementService uMgmSrv,
                                   UserLoginsRepository ulRepo,
                                   UserEventGateway rt,
                                   CachedMap<String, String> lockC,
                                   @Autowired(required = false)
                                   FcmTokenService fcmTkSrv
    ) {
        this.uMgm = uMgmSrv;
        this.userLoginRepo = ulRepo;
        this.userEventGateway = rt;
        this.lockCache = lockC;
        this.fcmTokenService = fcmTkSrv;
    }

    @Override
    @Transactional
    public String requestNotificationToUser(@RequestBody Message msg, Long userId) {
        if (userId > 0) {
            msg.setuId(userId);
            var u = uMgm.getCasinoUserById(userId).orElse(null);
            if (u == null) {
                return "user not found";
            }
            msg.setDestination(getDestination(msg.getType(), u, msg));
        }
        userEventGateway.sendNotificationRequest(msg);
        return "requested";
    }

    private String getDestination(NotificationType nType, CasinoUser user, Message msg) {
        return switch (nType) {
            case SMS -> NotificationUtils.reformatMobileNumber(user.getMobile());
            case EMAIL -> user.getEmail();
            case MOBILE_PUSH -> getCurrentFCM(user.getId());
            case SUPPORT, INFO -> msg.getDestination();
            default -> user.getDisplayName();
        };
    }


    /**
     * @param userId THE userId to get the latest FCM from
     * @return the most current FCM token for the user
     */
    @Override
    public String getCurrentFCM(long userId) {
        if (fcmTokenService != null) {
            return fcmTokenService.getLatestFcmTokenForUser(userId).orElse(null);
        }
        return null;
    }

    @Override
    @Transactional
    public void requestSupport(ContactRequest req, String addr, UserPrincipal currentUser) throws RequestThrottledException {
        sendNotification(addr, NotificationType.SUPPORT, req, currentUser);
    }

    private void sendNotification(String addr, NotificationType support, ContactRequest req, UserPrincipal currentUser) {
        if (lockCache.contains(addr)) {
            log.warn("Throttled for '{}'", addr);
            throw new RequestThrottledException();
        }
        var msg = new Message();
        msg.setType(support);
        msg.setMsg(req.getMessage());
        msg.setDestination(req.getEmail());
        msg.setName(req.getName());
        msg.setSubject(req.getTopic());
        msg.setTopic(req.getTopic());
        msg.setuId(-1L);
        if (currentUser != null) {
            msg.setuId(currentUser.getId());
            msg.getProperties().put("Language", currentUser.getLanguage());
            msg.getProperties().put("Displayname", currentUser.getDisplayName());
            msg.getProperties().put("Code-FB", currentUser.getLocalId());
            var tmpEmail = currentUser.getEmail();
            var localId = currentUser.getLocalId();
            if (!tmpEmail.startsWith(localId)) {
                msg.getProperties().put("Sys-Email", tmpEmail);
            }

        }
        userEventGateway.sendContactRequest(msg);
        lockCache.put(addr, "", 60, TimeUnit.SECONDS);
    }

    @Override
    @Transactional
    public void requestInfoContact(ContactRequest req, String addr, UserPrincipal currentUser) throws RequestThrottledException {
        sendNotification(addr, NotificationType.INFO, req, currentUser);
    }

    @EventListener
    public void handleCleanup(UserCleanupEvent evnt) {
        userLoginRepo.deleteAllByUserId(evnt.id());
    }
}
