package com.ously.gamble.webhook;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.api.consumable.ConsumableService;
import com.ously.gamble.api.consumable.CustomConsumableRequest;
import com.ously.gamble.api.consumable.UserConsumableAdminService;
import com.ously.gamble.api.cpopups.CPopupService;
import com.ously.gamble.api.marketingemails.MarketingEmailService;
import com.ously.gamble.api.popups.CasinoPopupRequest;
import com.ously.gamble.api.popups.PopupManagementService;
import com.ously.gamble.api.user.UserMessageService;
import com.ously.gamble.api.videoads.CrmVideoAdRequest;
import com.ously.gamble.api.videoads.VideoAdPushService;
import com.ously.gamble.api.videoads.VideoAdsService;
import com.ously.gamble.api.webhook.*;
import com.ously.gamble.payload.TxPrice;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.UUID;

@Service
public class WebhookServiceImpl implements WebhookService {
    private static final String[] EMPTY_STRING_ARRAY = new String[0];

    private final Logger log = LoggerFactory.getLogger(WebhookServiceImpl.class);

    private final ObjectMapper om;

    private final RabbitTemplate rabbitTemplate;

    private final AchievementService aSrv;

    private final PopupManagementService puMgmt;

    private final ConsumableService consService;

    private final UserMessageService uMsgService;

    private final RabbitTemplate rabbitTemplateNoTx;

    private final UserConsumableAdminService ucaS;

    private final VideoAdPushService videoAdPushService;
    private final VideoAdsService videoAdsService;

    private final SendFcmPushServiceImpl pushService;
    private final CPopupService cpuService;

    private final MarketingEmailService marketingEmailService;


    public WebhookServiceImpl(
            ObjectMapper om,
            @Qualifier("rabbitTemplate")
            RabbitTemplate rbTmpl,
            @Qualifier("rabbitTemplateNoTx")
            RabbitTemplate rbTmplNoTx,
            AchievementService aS,
            PopupManagementService puMs,
            ConsumableService cS,
            UserMessageService uMsS,
            UserConsumableAdminService ucaS,
            @Autowired(required = false) VideoAdPushService videoAdPushService,
            @Autowired(required = false) VideoAdsService videoAdsService,
            @Autowired(required = false) SendFcmPushServiceImpl pushService,
            @Autowired(required = false) CPopupService cpuServ,
            @Autowired(required = false) MarketingEmailService marketingEmailService) {
        this.puMgmt = puMs;
        this.uMsgService = uMsS;
        this.om = om;
        this.rabbitTemplate = rbTmpl;
        this.rabbitTemplateNoTx = rbTmplNoTx;
        this.aSrv = aS;
        this.consService = cS;
        this.ucaS = ucaS;
        this.videoAdPushService = videoAdPushService;
        this.videoAdsService = videoAdsService;
        this.pushService = pushService;
        this.cpuService = cpuServ;
        this.marketingEmailService = marketingEmailService;
    }


    /**
     * Receive and convert to real type (based on the action).
     * Then add it to the queue
     *
     * @param generic the generic request from outside
     * @return plain string msg
     */
    @Override
    public String processGenericRequest(String generic) {
        try {

            JsonNode jsonNode = om.readTree(generic);
            JsonNode actionNode = jsonNode.get("action");
            if (actionNode == null || !actionNode.isTextual()) {
                return ("Webhook without action was ignored:'" + generic + "'");
            }

            WebhookAction action = WebhookAction.valueOf(actionNode.textValue());

            switch (action) {
                case ADD_CPOPUP ->
                        rabbitTemplate.convertAndSend("webhook.add_popups", om.readValue(generic, WebhookAddPopupsRequest.class));
                case ADD_ACHIEVEMENT ->
                        rabbitTemplate.convertAndSend("webhook.add_achievement", om.readValue(generic, WebhookAddAchievementRequest.class));
                case ADD_POPUP ->
                        rabbitTemplateNoTx.convertAndSend("webhook.add_popup", om.readValue(generic, WebhookAddPopupRequest.class));
                case ADD_MESSAGE ->
                        rabbitTemplateNoTx.convertAndSend("webhook.add_message", om.readValue(generic, WebhookAddMessageRequest.class));
                case ADD_SPECIALOFFER_POPUP ->
                        rabbitTemplateNoTx.convertAndSend("webhook.add_specialoffer_popup", om.readValue(generic, WebhookAddSpecialofferPopupRequest.class));
                case ADD_CUSTOMOFFER ->
                        rabbitTemplateNoTx.convertAndSend("webhook.add_customoffer", om.readValue(generic, WebhookAddCustomConsumableRequest.class));
                case SHOW_VIDEOAD ->
                        rabbitTemplateNoTx.convertAndSend("webhook.show_video_ad", om.readValue(generic, WebhookVideoAdRequest.class));
                case SEND_FCM_PUSH ->
                        rabbitTemplateNoTx.convertAndSend("webhook.send_fcm", om.readValue(generic, WebhookSendFCMPushRequest.class));
                case UPDATE_MARKETING_EMAIL ->
                        rabbitTemplateNoTx.convertAndSend("webhook.update_marketing_email", om.readValue(generic, WebhookUpdateMarketingEmailRequest.class));
                default -> {
                    return ("Webhook with unknown action '{}' was ignored!");
                }
            }
            // now store webhook
            return "accepted";
        } catch (JsonProcessingException e) {
            log.warn("Invalid WebhookRequest:{} -> {}", generic, e.getMessage());
            return "Could not convert request: {}" + e.getMessage();
        }
    }

    @Override
    public void addAchievementRequest(WebhookAddAchievementRequest whaar) {
        try {
            var uid = Long.parseLong(whaar.getUserId().replace("uid_", ""));
            TxPrice.parsePriceDef(whaar.getRewards());
            var achievement = aSrv.createAchievement(uid, AchievementType.CUSTOM, whaar.getQualifier(), whaar.getRewards(), whaar.getTitle(), whaar.getMessage(), whaar.getVariables());
            if (achievement == null) {
                log.warn("Webhook add_achievement duplicate achievement sent:{}", whaar);
            }
        } catch (Exception e) {
            log.warn("Error processing addAchievement-request:{}", whaar, e);
        }
    }

    @Override
    public void addPopupRequest(WebhookAddPopupRequest whpur) {
        try {
            var uid = Long.parseLong(whpur.getUserId().replace("uid_", ""));
            var popupDefinitionId = whpur.getPopupDefinitionId();
            var selector = whpur.getSelector();
            String[] vars;
            if (whpur.getVariables() != null && !whpur.getVariables().isEmpty()) {
                vars = whpur.getVariables().entrySet().stream().map(e -> e.getKey() + '=' + e.getValue()).toArray(String[]::new);
            } else {
                vars = EMPTY_STRING_ARRAY;
            }
            var cpr = new CasinoPopupRequest(uid, popupDefinitionId, selector, Instant.now().plus(4, ChronoUnit.HOURS), vars);
            puMgmt.createPopupIfNotExists(cpr, uid);
        } catch (Exception e) {
            log.warn("Error processing addPopup-request:{}", whpur, e);
        }
    }

    @Override
    public void addSpecialofferPopupRequest(WebhookAddSpecialofferPopupRequest whapr) {
        try {
            var uid = Long.parseLong(whapr.getUserId().replace("uid_", ""));
            var allActiveSpecialoffers = consService.getAllActiveSpecialoffers(whapr.getGamePlatform(), whapr.getOffsetMinutes());
            if (allActiveSpecialoffers.isEmpty()) {
                return;
            }

            var pid = allActiveSpecialoffers.getFirst().getId();
            if (allActiveSpecialoffers.size() > 1) {
                pid = allActiveSpecialoffers.get((int) (Math.random() * allActiveSpecialoffers.size())).getId();
            }

            var cpr = new CasinoPopupRequest(uid, whapr.getPopupDefinitionId(), null, Instant.now().plus(4, ChronoUnit.HOURS), new String[]{"productid=" + pid});
            puMgmt.createPopupIfNotExists(cpr, uid);
        } catch (Exception e) {
            log.warn("Error processing addPopup-request:{}", whapr, e);
        }
    }

    @Override
    public void addMessageRequest(WebhookAddMessageRequest whmr) {
        try {
            var uid = Long.parseLong(whmr.getUserId().replace("uid_", ""));
            var type = Objects.requireNonNullElse(whmr.getType(), UserMessageType.CUSTOM);
            var title = Objects.requireNonNullElseGet(whmr.getTitle(), type::title);
            var body = Objects.requireNonNullElseGet(whmr.getBody(), type::body);

            var qualifier = Objects.requireNonNullElseGet(whmr.getQualifier(),
                    () -> UUID.randomUUID().toString());

            var umc = new UserMessageContent(type,
                    title, body, whmr.getTtl(), whmr.getVariables(), whmr.getActions());
            uMsgService.createNewUserMessage(uid, qualifier, umc, whmr.isPopup(), false);
        } catch (Exception e) {
            log.warn("Error processing addMessage-request:{}", whmr, e);
        }
    }

    @Override
    public void addBonusRequest(WebhookAddBonusRequest whabr) {

    }

    @Override
    public void addPromotionLinkRequest(WebhookAddPromotionLinkRequest whamr) {

    }

    @Override
    public void addCustomOffer(WebhookAddCustomConsumableRequest whapr) {
        ucaS.addUserConsumable(new CustomConsumableRequest(whapr, true));
    }

    @Override
    public void showVideoAd(WebhookShowVideoAdRequest whapr) {
        Objects.requireNonNull(videoAdPushService);

        if (whapr instanceof WebhookVideoAdRequest enhancedRequest && videoAdsService != null) {
            CrmVideoAdRequest crmRequest = new CrmVideoAdRequest(
                whapr.getUserId(),
                enhancedRequest.getPosition(),
                enhancedRequest.getCoinAmount(),
                enhancedRequest.getContext()
            );
            videoAdsService.prepare(crmRequest);
        } else {
            videoAdPushService.sendVideoAdPush(whapr.getUserId());
        }
    }

    @Override
    public void sendFcmPush(WebhookSendFCMPushRequest whsfcmr) {
        Objects.requireNonNull(pushService);
        pushService.sendFcmPush(whsfcmr.getUserId(), whsfcmr.getEventName(), whsfcmr.getParameters());
    }

    @Override
    public void addPopupsRequest(WebhookAddPopupsRequest whpur) {
        Objects.requireNonNull(cpuService);
        cpuService.createPopupFromWebhook(whpur);
    }

    @Override
    public void updateMarketingEmailRequest(WebhookUpdateMarketingEmailRequest whcmer) {
        Objects.requireNonNull(marketingEmailService);
        marketingEmailService.confirmEmail(whcmer.getUserId(), whcmer.getEmail());
    }
}
