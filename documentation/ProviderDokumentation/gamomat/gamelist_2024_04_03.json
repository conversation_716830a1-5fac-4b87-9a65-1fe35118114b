{"games": [{"gameId": "100flaringfruits", "gameName": "100 Flaring Fruits", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [100, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "20<PERSON><PERSON><PERSON><PERSON>s", "gameName": "20 Ember Wilds", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [20, 40, 60, 80, 100, 120, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "20flaringfruits", "gameName": "20 Flaring Fruits", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [20, 40, 60, 80, 100, 120, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "40finestxxl", "gameName": "40 Finest XXL", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 160, 180, 200, 240, 300, 400, 450, 600, 800, 900, 1200, 1600, 1800, 2400], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "50flaringfruits", "gameName": "50 Flaring Fruits", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [50, 100, 150, 200, 250, 300, 500, 750, 1000, 1500, 2000, 3000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "5<PERSON>berwilds", "gameName": "5 Ember Wilds", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "5flaringfruits", "gameName": "5 Flaring Fruits", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "alexandriafire", "gameName": "Alexandria Fire", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ancientmagic", "gameName": "Ancient Magic", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ancientriches", "gameName": "Ancient Riches", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ancientrichesrhfp", "gameName": "Ancient Riches RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "atlanticwilds", "gameName": "Atlantic Wilds", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Aura of Jupiter", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "backtothefruits", "gameName": "Back to the Fruits", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "backtothefruitsroar", "gameName": "Back to the Fruits ROAR", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [20, 40, 60, 80, 100, 120, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "beautifulnature", "gameName": "Beautiful Nature", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "beerparty", "gameName": "Beer Party", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "blackbeauty", "gameName": "Black Beauty", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofelements", "gameName": "Book of Elements", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [20, 40, 60, 80, 100, 120, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofjuno", "gameName": "Book of Juno", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofmadness", "gameName": "Book of Madness", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofmadnessroar", "gameName": "Book of Madness ROAR", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Book of <PERSON>huhn", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>gon<PERSON>", "gameName": "Book of Moorhuhn GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookof<PERSON><PERSON><PERSON>gonimin", "gameName": "Book Of Moorhuhn GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofoa<PERSON>", "gameName": "Book Of Oasis", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofromeoandjulia", "gameName": "Book of Romeo & Julia", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofromeoan<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Book Of Romeo and Julia GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookofromeoandjuliagon<PERSON>in", "gameName": "Book Of Romeo and Julia GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "bookoftheages", "gameName": "Book of the Ages", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 35, 50, 70, 75, 100, 150, 200, 250, 300, 350, 500, 700, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandbounties", "gameName": "Books and Bounties", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandbulls", "gameName": "Books & Bulls", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandbullsgoni", "gameName": "Books & Bulls GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksand<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Books & Bulls GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandbullsrhfp", "gameName": "Books & Bulls RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandbullsrhfpmin", "gameName": "Books & Bulls RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandpearls", "gameName": "Books and Pearls", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandpearlsroar", "gameName": "Books and Pearls ROAR", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "booksandtemples", "gameName": "Books and Temples", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 30, 40, 50, 60, 80, 100, 150, 200, 300, 400, 500, 600, 800, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalball", "gameName": "Crystal Ball", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballdeluxe", "gameName": "Crystal Ball Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballgoni", "gameName": "Crystal Ball GONI ", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballgonimin", "gameName": "Crystal Ball GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballmultisymbols", "gameName": "Crystal Ball Multi Symbols", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballrhfp", "gameName": "Crystal Ball RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalballrhfpmin", "gameName": "Crystal Ball RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "crystalburstxxl", "gameName": "Crystal Burst XXL", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 160, 180, 200, 240, 300, 400, 450, 600, 800, 900, 1200, 1600, 1800, 2400], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "cutie<PERSON>", "gameName": "<PERSON><PERSON>", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "discofathena", "gameName": "Disc of Athena", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 50, 70, 100, 150, 200, 300, 500, 700, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "divinefire", "gameName": "Divine Fire", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "dragonoftheprincess", "gameName": "Dragon of the Princess", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "duckshooter", "gameName": "<PERSON> Shooter", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "expandingfireworks", "gameName": "Expanding Fireworks", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "explodiac", "gameName": "Explodiac", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "explodiacmp", "gameName": "Explodiac Maxi Play", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "explodiacrhfp", "gameName": "Explodiac RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruits", "gameName": "Fancy Fruits", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsdeluxe", "gameName": "Fancy Fruits Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsgoni", "gameName": "Fancy Fruits GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsgonimin", "gameName": "Fancy Fruits GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsrhfp", "gameName": "Fancy Fruits RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsrhfpmin", "gameName": "Fancy Fruits RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fancyfruitsroar", "gameName": "Fancy Fruits ROAR", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fengsfortune", "gameName": "<PERSON><PERSON>", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "footballsuperspins", "gameName": "Football Super Spins", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "forever<PERSON><PERSON><PERSON>", "gameName": "Forever Diamonds", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON>", "gameName": "Fort Brave", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitlove", "gameName": "Fruit Love", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 160, 180, 200, 240, 300, 400, 450, 600, 800, 900, 1200, 1600, 1800, 2400], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmania", "gameName": "Fruit Mania", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmaniadeluxe", "gameName": "Fruit Mania Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmaniagoni", "gameName": "Fruit Mania GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmaniagonimin", "gameName": "Fruit Mania GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmaniarhfp", "gameName": "Fruit Mania RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitmaniarhfpmin", "gameName": "Fruit Mania RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "fruitrush", "gameName": "Fruit Rush", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [20, 40, 60, 80, 100, 120, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "gatesofpersia", "gameName": "Gates of Persia", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "glamoroustimes", "gameName": "Glamorous Times", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Golden Ei of Moorhuhn", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "greatwarrior", "gameName": "Great Warrior", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON>", "gameName": "King & Queen", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "King of the Jungle", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "king<PERSON>hejunglegoni", "gameName": "King of the Jungle GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "kingofthejunglerhfp", "gameName": "King of the Jungle RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "La Dolce Vita", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ladolcevitadeluxe", "gameName": "La Dolce Vita Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "La Dolce Vita GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "La Dolce Vita GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ladolcevitarhfp", "gameName": "La Dolce Vita RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ladolcevitarhfpmin", "gameName": "La Dolce Vita RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "landofheroes", "gameName": "Land of Heroes", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON>ofheroesgon<PERSON>", "gameName": "Land of Heroes GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "lavalions", "gameName": "Lava Lions", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Maaax Diamonds", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "maaaxdiamondsdeluxe", "gameName": "Maaax Diamonds Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Maaax Diamonds GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "maaaxdiamondsrhfp", "gameName": "Maaax Diamonds RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Maaax Diamonds Christmas Edition", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "magicstone", "gameName": "Magic Stone", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "mayanfire", "gameName": "Mayan Fire", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "mightydragon", "gameName": "Mighty Dragon", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "monkeymania", "gameName": "Monkey Mania", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 90, 100, 120, 150, 180, 200, 300, 400, 450, 600, 800, 900, 1200, 1800], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "nightwolves", "gameName": "Night Wolves", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "nordicfire", "gameName": "Nordic Fire", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "oldfisherman", "gameName": "Old Fisherman", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "phantom<PERSON><PERSON><PERSON><PERSON>", "gameName": "Phantoms Mirror", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 40, 50, 100, 200, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "pharaos<PERSON><PERSON>", "gameName": "Ph<PERSON><PERSON>", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Pharaos Riches GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "pharaosrichesrhfp", "gameName": "Pharaos Riches RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbook", "gameName": "<PERSON><PERSON>", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookdeluxe", "gameName": "Ramses Book Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": false, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookeaster", "gameName": "<PERSON><PERSON> Book Easter", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON>gon<PERSON>", "gameName": "Ramses Book GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "<PERSON>es Book GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookrhfp", "gameName": "Ramses Book RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookrhfpmin", "gameName": "<PERSON>es Book RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookroar", "gameName": "Ramses Book ROAR", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "ramsesbookxmas", "gameName": "Ramses Book Christmas Edition", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "romanlegion", "gameName": "Roman Legion", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "romanlegiongoni", "gameName": "Roman Legion GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "romanleg<PERSON><PERSON><PERSON><PERSON>", "gameName": "Roman Legion GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "romanlegionxtreme", "gameName": "Roman Legion Xtreme", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [20, 40, 100, 200, 400, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "romanlegionxtremerhfp", "gameName": "Roman Legion Xtreme RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [20, 40, 100, 200, 400, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON>", "gameName": "Royal Seven", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevendeluxe", "gameName": "Royal Seven Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevengon<PERSON>", "gameName": "Royal Seven GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Royal Seven GONI Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenultra", "gameName": "Royal Seven Ultra", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [20, 40, 60, 80, 100, 120, 160, 200, 240, 300, 400, 600, 800, 1200, 1600, 2400], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenxxl", "gameName": "Royal Seven XXL", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenxxldeluxe", "gameName": "Royal Seven XXL Deluxe", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenxxleaster", "gameName": "Royal Seven XXL Easter", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 80, 100, 120, 150, 200, 300, 400, 600, 800, 1200], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenxxlrhfp", "gameName": "Royal Seven XXL RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "royalsevenxxlrhfpmin", "gameName": "Royal Seven XXL RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "savannamoon", "gameName": "Savanna Moon", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "sevensandbooks", "gameName": "Sevens & Books", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "sevensfire", "gameName": "Sevens Fire", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "shogunssecret", "gameName": "Shoguns Secret", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 40, 50, 60, 75, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "simplythebest", "gameName": "Simply The Best", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "stacksofjacks", "gameName": "Stacks Of Jacks", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "stickydiamonds", "gameName": "Sticky Diamonds", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "sticky<PERSON>mond<PERSON>ster", "gameName": "Sticky Diamonds Easter", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "stickydiamondsrhfp", "gameName": "Sticky Diamonds RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "stickydiamondsrhfpmin", "gameName": "Sticky Diamonds RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON>se<PERSON><PERSON>", "gameName": "Sunny Sevens", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "superdupercherry", "gameName": "Super Duper Cherry", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 40, 50, 100, 200, 250, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "superdupercherryrhfp", "gameName": "Super Duper Cherry RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 40, 50, 100, 200, 250, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "super<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Super Duper Moor<PERSON>hn", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 40, 50, 100, 200, 250, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "superdupermoorhuhneaster", "gameName": "Super Duper Moorhuhn Easter", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 40, 50, 100, 200, 250, 400, 500, 1000, 2000, 4000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "take5", "gameName": "Take 5", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "take5goni", "gameName": "Take 5 GONI", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "take5rhfp", "gameName": "Take 5 RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "take5rhfpmin", "gameName": "Take 5 RHFP Min", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "take5xmas", "gameName": "Take 5 Christmas Edition", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "thebookbeyond", "gameName": "The Book Beyond", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "themightyking", "gameName": "The Mighty King", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 70, 90, 100, 140, 150, 200, 210, 300, 400, 450, 500, 600, 700, 900, 1000, 1400, 1500, 2000, 2100, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "towerofpower", "gameName": "Tower of Power", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "vegasfruits", "gameName": "Vegas Fruits", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "vegasjoker", "gameName": "Vegas Joker", "gameProviderId": "spawn", "gameProviderName": "Spawn", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "nl", "social", "uk"], "betSizes": [5, 10, 25, 50, 100, 250, 500, 1000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "westernjack", "gameName": "Western Jack", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON>", "gameName": "Wild Rapa Nui", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 500, 600, 1000, 1500, 2000, 3000, 4000, 6000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "wildrubies", "gameName": "<PERSON>", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "<PERSON> Rubies <PERSON>", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "social", "uk"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "wildrubiesrhfp", "gameName": "Wild Rubies RHFP", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameName": "Wild Rubies Christmas Edition", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 50, 100, 200, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "wildsgonewild", "gameName": "Wilds Gone Wild", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "winblaster", "gameName": "<PERSON> Blaster", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 30, 40, 50, 100, 150, 200, 300, 400, 500], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "winblasterxmas", "gameName": "Win Blaster Christmas Edition", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 20, 30, 40, 50, 100, 150, 200, 300, 400, 500], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "winshooter", "gameName": "Win Shooter", "gameProviderId": "flow", "gameProviderName": "Flow", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [5, 10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}, {"gameId": "xplodingpumpkins", "gameName": "Xploding Pumpkins", "gameProviderId": "client1", "gameProviderName": "Client1", "gameStudioId": "gamomat", "gameStudioName": "Gamomat", "gameStatus": "active", "theoreticalRTP": 0, "jurisdictions": ["de", "de_ur", "es", "mt", "uk", "social"], "betSizes": [10, 20, 30, 40, 50, 60, 100, 150, 200, 300, 400, 600], "freeRoundSupport": true, "featureTriggerSupport": false, "gameMedia": {"thumbnails": [], "backgrounds": []}, "category": "slot"}]}