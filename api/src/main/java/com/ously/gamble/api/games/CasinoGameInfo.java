package com.ously.gamble.api.games;

import com.ously.gamble.persistence.model.game.GameGenre;
import com.ously.gamble.persistence.model.game.GameJackpotMode;
import com.ously.gamble.persistence.model.game.GameType;
import com.ously.gamble.persistence.model.game.GameView;

import java.time.Instant;
import java.time.LocalDate;

public class CasinoGameInfo {
    private final Long id;
    private final String gameId;
    private final String name;
    private final boolean active;
    private final boolean activeIos;
    private final boolean activeMobile;
    private final boolean activeDesktop;
    private final boolean activeAndroid;

    private final GameGenre genre;
    private final GameType type;
    private final GameJackpotMode jackpotMode;

    private final Long sortOrder;
    private final Instant updatedAt;
    private final Instant createdAt;
    private final boolean isLinked;
    private final Long infoId;
    private final String layout;
    private final Double officialRtp;
    private final Long slotrank;
    private final Long paylines;
    private final LocalDate releaseDate;
    private final String slotType;
    private final String volatility;
    private final Long spins;

    private final Double gameRtp;

    private final Integer unlockLevel;
    private final Double realRtp;
    private final Long sessions;
    private final Double sumBet;
    private final Double sumWin;
    private final Double avgSessionTime;
    private final boolean vendorActive;
    private final String blockedCountries;
    private final Integer vendorId;
    private final String vendorName;
    private final String vendorHomepage;
    private final String promoType;
    private final String thumbSourceDimension;
    private final boolean hasThumb;
    private final Long issuesLastMonth;

    private final Instant keyChangedAt;
    private final boolean valid;
    private final String tags;

    public CasinoGameInfo(GameView v) {

        this.id = v.getId();
        this.gameId = v.getGameId();
        this.name = v.getName();
        this.active = v.isActive();
        this.activeIos = v.isActiveIos();
        this.activeMobile = v.isActiveMobile();
        this.activeDesktop = v.isActiveDesktop();
        this.activeAndroid = v.isActiveAndroid();
        this.genre = v.getGenre();
        this.type = v.getType();
        this.jackpotMode = v.getJackpotMode();
        this.sortOrder = v.getSortOrder();
        this.updatedAt = v.getUpdatedAt();
        this.createdAt = v.getCreatedAt();
        this.isLinked = v.isLinked();
        this.infoId = v.getInfoId();
        this.layout = v.getLayout();
        this.officialRtp = v.getOfficialRtp();
        this.gameRtp = v.getGameRtp();
        this.unlockLevel = v.getUnlockLevel();
        this.slotrank = v.getSlotrank();
        this.paylines = v.getPaylines();
        this.releaseDate = v.getReleaseDate();
        this.slotType = v.getSlotType();
        this.volatility = v.getVolatility();
        this.spins = v.getSpins();
        this.realRtp = v.getRealRtp();
        this.sessions = v.getSessions();
        this.sumBet = v.getSumBet();
        this.sumWin = v.getSumWin();
        this.avgSessionTime = v.getAvgSessionTime();
        this.vendorActive = v.isVendorActive();
        this.blockedCountries = v.getBlockedCountries();
        this.vendorId = v.getVendorId();
        this.vendorName = v.getVendorName();
        this.vendorHomepage = v.getVendorHomepage();
        this.promoType = v.getPromoType();
        this.thumbSourceDimension = v.getThumbSourceDimension();
        this.hasThumb = v.isHasThumb();
        this.issuesLastMonth = v.getIssuesLastMonth();
        this.keyChangedAt = v.getKeyChangedAt();
        this.valid = v.isValid();
        this.tags = v.getTags();
    }

    public Long getId() {
        return id;
    }

    public String getGameId() {
        return gameId;
    }

    public String getName() {
        return name;
    }


    public Long getSortOrder() {
        return sortOrder;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }


    public Long getInfoId() {
        return infoId;
    }

    public String getLayout() {
        return layout;
    }

    public Double getOfficialRtp() {
        return officialRtp;
    }

    public Long getSlotrank() {
        return slotrank;
    }

    public Long getPaylines() {
        return paylines;
    }

    public LocalDate getReleaseDate() {
        return releaseDate;
    }

    public String getSlotType() {
        return slotType;
    }

    public String getVolatility() {
        return volatility;
    }

    public Long getSpins() {
        return spins;
    }

    public Double getRealRtp() {
        return realRtp;
    }

    public Long getSessions() {
        return sessions;
    }

    public Double getSumBet() {
        return sumBet;
    }

    public Double getSumWin() {
        return sumWin;
    }

    public Double getAvgSessionTime() {
        return avgSessionTime;
    }

    public String getBlockedCountries() {
        return blockedCountries;
    }

    public String getVendorName() {
        return vendorName;
    }

    public String getVendorHomepage() {
        return vendorHomepage;
    }

    public String getPromoType() {
        return promoType;
    }

    public String getThumbSourceDimension() {
        return thumbSourceDimension;
    }

    public boolean isActive() {
        return active;
    }

    public boolean isActiveIos() {
        return activeIos;
    }

    public boolean isActiveMobile() {
        return activeMobile;
    }

    public boolean isActiveDesktop() {
        return activeDesktop;
    }

    public boolean isActiveAndroid() {
        return activeAndroid;
    }

    public boolean isLinked() {
        return isLinked;
    }

    public boolean isVendorActive() {
        return vendorActive;
    }

    public boolean isHasThumb() {
        return hasThumb;
    }

    public Long getIssuesLastMonth() {
        return issuesLastMonth;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public Double getGameRtp() {
        return gameRtp;
    }

    public Integer getUnlockLevel() {
        return unlockLevel;
    }

    public Instant getKeyChangedAt() {
        return keyChangedAt;
    }

    public boolean isValid() {
        return valid;
    }

    public String getTags() {
        return tags;
    }

    public GameGenre getGenre() {
        return genre;
    }

    public GameType getType() {
        return type;
    }

    public GameJackpotMode getJackpotMode() {
        return jackpotMode;
    }
}
