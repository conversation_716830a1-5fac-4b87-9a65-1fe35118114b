package com.ously.gamble.api.cache;


import com.github.benmanes.caffeine.cache.LoadingCache;

import java.util.Map;
import java.util.function.Function;

public interface LCacheFactory<K, V> {

    /**
     * @param name        name of the cache
     * @param maxsize     maximum number of entries
     * @param concurrency not used - it runs in the callers thread (and transactional context)
     * @param ttlSeconds  expiry after write
     * @return a fully configured cache shared throughout the process
     */
    LoadingCache<K, V> registerCacheLoader(String name, long maxsize, int concurrency,
                                           int ttlSeconds,
                                           LCacheLoader<K, V> loader);

    /**
     * Registers a LoadingCache with dedicated transaction support. The loader runs asynchronously using a ThreadpoolExecutor.
     * (it uses the main pool)
     *
     * @param name        name of the cache
     * @param maxsize     maximum number of entries
     * @param concurrency defines how many concurrent accesses (and running loaders) are allowed
     * @param ttlSeconds  expiry after write
     * @param action      the loader function
     * @return a fully configured cache shared throughout the process
     * @deprecated due to its potential lockup deficiencies (esp. when a loader action uses other transactional loaders) it is
     * highly recommended to not use them except its loader has a very well defined boundary. Its hard to debug and should be
     * avoided!
     */
    @Deprecated()
    LoadingCache<K, V> registerTxCacheLoader(String name, long maxsize, int concurrency,
                                             int ttlSeconds, Function<K, V> action);

    /**
     * Registers a LoadingCache with dedicated transaction support. The loader runs asynchronously using a ThreadpoolExecutor.
     *
     * @param name        name of the cache
     * @param maxsize     maximum number of entries
     * @param concurrency defines how many concurrent accesses (and running loaders) are allowed
     * @param ttlSeconds  expiry after write
     * @param action      the loader function
     * @param useReplica  if true the loader uses the replica datasource
     * @return a fully configured cache shared throughout the process
     * @deprecated due to its potential lockup deficiencies (esp. when a loader action uses other transactional loaders) it is
     * highly recommended to not use them except its loader has a very well defined boundary. Its hard to debug and should be
     * avoided!
     */
    @Deprecated()
    LoadingCache<K, V> registerTxCacheLoader(String name, long maxsize, int concurrency,
                                             int ttlSeconds, Function<K, V> action,
                                             boolean useReplica);

    void clearAllMaps();

    Map<String, CacheStatus> getCacheMapSizes();

    void clearSpecificCache(String cacheName, Boolean clearAll, Object clearKey);
}
