create
    procedure ARCHIVE_TRANSACTIONS(IN p_partition varchar(64), IN p_minage_hours int)
BEGIN
    DECLARE rows_l INT;
    DECLARE barrier_ts DATETIME;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            SELECT 'An error has occurred, operation rollbacked and the stored procedure was terminated';
        END;

    SET rows_l = 1;
    SET barrier_ts = DATE_SUB(CURRENT_TIMESTAMP, INTERVAL p_minage_hours HOUR);

    WHILE rows_l > 0
        DO




            START TRANSACTION;
            SET @insert_query = CONCAT('INSERT IGNORE INTO ctransactions select id,balance_after,bet,win,created_at,ext_tx_numid,ext_tx_id,type,vendor_name,game_id,user_id,earned_xp,level_after,percnl_after,round_ref,session_id,cancelled,boost from transactions partition (',
                                       p_partition,
                                       ')  where created_at <= "', barrier_ts, '" ORDER BY ID LIMIT 10000;');
            PREPARE archive_stmt FROM @insert_query;
            EXECUTE archive_stmt;
            DEALLOCATE PREPARE archive_stmt;

            SET @delete_query = CONCAT('DELETE from transactions partition (', p_partition,
                                       ')  where created_at <= "',
                                       barrier_ts,
                                       '" ORDER BY ID LIMIT 10000;');
            PREPARE del_stmt FROM @delete_query;
            EXECUTE del_stmt;
            SET rows_l =  row_count();
            DEALLOCATE PREPARE del_stmt;
            COMMIT;
            select sleep(1);
        END WHILE;
END;

