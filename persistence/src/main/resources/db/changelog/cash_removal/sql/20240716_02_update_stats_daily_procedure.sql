create
   procedure REPLACE_STATS_DAILY(IN statsdate date, IN social bit) modifies sql data
BEGIN

    DELETE
    from stats_daily
    where rdate = statsdate;

    insert into stats_daily (rdate, bets_count, wins_count, bets_sum, wins_sum,
                             boosts_sum)
    select statsdate              as rdate,
           sum(s.num_bets)        as bets_count,
           sum(s.num_wins)        as wins_count,
           sum(s.sum_bet) / 100   as bets_sum,
           sum(s.sum_win) / 100   as wins_sum,
           sum(s.sum_boost) / 100 as boosts_sum
    from session_statistics_rd s
    where s.rdate = statsdate
    group by rdate;

    insert into stats_daily (rdate, ftds)
    select *
    from (select statsdate as rdate, count(*) as ftds
          from user_stats_daily
          where rdate = statsdate
            AND depos != 0
            AND depos_total = depos) new
    on duplicate key
        update ftds = new.ftds;

    insert into stats_daily (rdate, active_players)
    select *
    from (select statsdate as rdate, count(DISTINCT user_id) as active_players
          from user_stats_daily ul
          where ul.rdate = statsdate) new
    on duplicate key
        update active_players = new.active_players;


    insert into stats_daily (rdate, sign_ups)
    select *
    from (select statsdate as rdate, count(*) as sign_ups
          from users u
          where u.created_at >= statsdate
            and u.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update sign_ups = new.sign_ups;

    insert into stats_daily (rdate, deposits_count, deposits_user_count, deposits_sum)
    select *
    from (select statsdate                      as rdate,
                 count(*)                       as deposits_count,
                 count(DISTINCT user_id)        as deposits_user_count,
                 COALESCE(sum(applied_cost), 0) as deposits_sum
          from purchases pd
          where pd.created_at >= statsdate
            and pd.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update deposits_count      = new.deposits_count,
               deposits_user_count = new.deposits_user_count,
               deposits_sum        = new.deposits_sum;

END;

