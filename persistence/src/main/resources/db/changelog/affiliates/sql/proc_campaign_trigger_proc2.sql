create procedure ADD_CAMPAIGN_HISTORY(
    IN x_user_id bigint, IN x_num int, IN x_dt DATE,
    IN n_admin_fee DECIMAL(4, 2), IN n_rs_deposits DECIMAL(4, 3), IN n_rs_ggr DECIMAL(4, 3),
    IN n_rs_wagers DECIMAL(4, 3), IN n_cpa DECIMAL(5), IN n_cpa_baseline DECIMAL(6, 2),
    IN o_admin_fee DECIMAL(4, 2), IN o_rs_deposits DECIMAL(4, 3), IN o_rs_ggr DECIMAL(4, 3),
    IN o_rs_wagers DECIMAL(4, 3), IN o_cpa DECIMAL(5), IN o_cpa_baseline DECIMAL(6, 2))
    modifies sql data
BEGIN
    IF (
            (n_admin_fee != coalesce(o_admin_fee, 99999)) OR
            (n_cpa <> coalesce(o_cpa, 99999)) OR
            (n_cpa_baseline <> coalesce(o_cpa_baseline, 99999)) OR
            (n_rs_deposits <> coalesce(o_rs_deposits, 99999)) OR
            (n_rs_ggr <> coalesce(o_rs_ggr, 99999)) OR
            (n_rs_wagers <> coalesce(o_rs_wagers, 99999))
        ) THEN

        INSERT into aff_campaigns_history (user_id, num, rdate, admin_fee, rs_deposits, rs_wagers, rs_ggr, cpa,
                                           cpa_baseline)
        values (x_user_id, x_num, x_dt, n_admin_fee, n_rs_deposits, n_rs_wagers, n_rs_ggr, n_cpa, n_cpa_baseline)
        on duplicate key update admin_fee=n_admin_fee,
                                rs_ggr = n_rs_ggr,
                                rs_deposits=n_rs_deposits,
                                rs_wagers=n_rs_wagers,
                                cpa=n_cpa,
                                cpa_baseline=n_cpa_baseline;
    END IF;
END;
