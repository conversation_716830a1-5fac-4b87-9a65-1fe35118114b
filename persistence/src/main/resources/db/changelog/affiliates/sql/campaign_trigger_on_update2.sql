drop trigger aff_campaign_on_update;
create trigger aff_campaign_on_update
    after update
    on aff_campaigns
    for each row
    CALL ADD_CAMPAIGN_HISTORY(NEW.user_id, NEW.num, DATE_ADD(CURDATE(), INTERVAL 1 DAY),
                              NEW.admin_fee, NEW.rs_deposits, NEW.rs_ggr, NEW.rs_wagers, NEW.cpa, NEW.cpa_baseline,
                              OLD.admin_fee, OLD.rs_deposits, OLD.rs_ggr, OLD.rs_wagers, OLD.cpa, OLD.cpa_baseline);

