update aff_links al join (select al.ref_id as refId, max(ach.rdate) as achDate
                          from aff_links al,
                               aff_campaigns_history ach
                          where ach.num = al.num
                            and ach.user_id = al.user_id
                            and ach.rdate <= DATE(al.created_at)
                          group by al.ref_id) xx on al.ref_id = xx.refId join aff_campaigns_history ach on
            al.user_id = ach.user_id and al.num = ach.num and xx.achDate = ach.rdate
set al.cpa=ach.cpa,
    al.cpa_baseline=ach.cpa_baseline;