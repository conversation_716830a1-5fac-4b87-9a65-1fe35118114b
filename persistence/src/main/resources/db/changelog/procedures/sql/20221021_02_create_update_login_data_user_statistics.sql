CREATE PROCEDURE UPDATE_LOGIN_DATA_USER_STATISTICS(IN x_user_id BIGINT, IN x_login_ts TIMESTAMP)
    MODIFIES SQL DATA
BEGIN

    INSERT into user_statistics (user_id, ss_session_count, ss_first_session_at, ss_last_session_at, ss_num_spins,
                                 ss_num_wins, ss_sum_bet, ss_sum_win, ss_total_session_sec, first_login, last_login,
                                 logins_web,logins_ios,logins_android,
                                 num_purchases ,sum_purchases ,first_purchase ,last_purchase
                                 )
    select *
    from (
             select x_user_id                                                          as user_id,
                    0                                                                as ss_session_count,
                    null                                                             as ss_first_session_at,
                    null                                                             as ss_last_session_at,
                    0                                                                as ss_num_spins,
                    0                                                                as ss_num_wins,
                    0                                                                as ss_sum_bet,
                    0                                                                as ss_sum_win,
                    0                                                                as ss_total_session_sec,
                    x_login_ts                                                       as first_login,
                    x_login_ts                                                       as last_login,
                    count(distinct IF(platform = 'WEB', DATE(created_at), null))     as logins_web,
                    count(distinct IF(platform = 'IOS', DATE(created_at), null))     as logins_ios,
                    count(distinct IF(platform = 'ANDROID', DATE(created_at), null)) as logins_android,
                    0 as num_purchases, 0 as sum_purchases, null as first_purchase,null as last_purchase
             from user_login
             where user_id = x_user_id) new on duplicate key update
                                                                     user_statistics.first_login = LEAST(COALESCE(user_statistics.first_login, '2035-01-01'), new.first_login),
                                                                     user_statistics.last_login  = GREATEST(COALESCE(user_statistics.last_login, '2001-01-01'), new.last_login),
                                                                     user_statistics.logins_web = new.logins_web,
                                                                     user_statistics.logins_ios = new.logins_ios,
                                                                     user_statistics.logins_android= new.logins_android;
END;
