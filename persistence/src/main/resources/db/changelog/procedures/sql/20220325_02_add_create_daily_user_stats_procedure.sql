create
    procedure REPLACE_USER_DAILY_STATS(IN statsdate DATE, IN social bit(1))
    modifies sql data
BEGIN

    # Delete entries
    DELETE from user_stats_daily where rdate = statsdate;

    # Insert bet/win stats
    # session_transaction

    insert into user_stats_daily (rdate, user_id, bets, wins)
    select statsdate as rdate,s.user_id as user_id, sum(st.bet) as bets, sum(st.win) as wins
    from session s
             join session_transaction st
                  on s.user_id = st.user_id and s.session_id = st.session_id
    where s.created_at >= DATE_SUB(statsdate, INTERVAL 1 DAY )
      and s.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
      and st.created_at >= statsdate
      and st.created_at < DATE_ADD(statsdate,INTERVAL 1 DAY)
      and st.cancelled = false
      and st.type in ('BET', 'WIN', 'DIRECTWIN')
    group by s.user_id;

    # ctransactions
    insert into user_stats_daily (rdate, user_id, bets, wins)
    select * from (
                      select statsdate as rdate,st.user_id as user_id, sum(st.bet) as nbets, sum(st.win) as nwins
                      from ctransactions st
                      where st.created_at >= statsdate
                        and st.created_at < DATE_ADD(statsdate,INTERVAL 1 DAY)
                        and st.cancelled = false
                        and st.type in ('BET', 'WIN', 'DIRECTWIN')
                      group by st.user_id) new
    on duplicate key update bets=bets+new.nbets, wins=wins+new.nwins;

    # transactions
    insert into user_stats_daily (rdate, user_id, bets, wins)
    select * from (
                      select statsdate as rdate,st.user_id as user_id, sum(st.bet) as nbets, sum(st.win) as nwins
                      from transactions st
                      where st.created_at >= statsdate
                        and st.created_at < DATE_ADD(statsdate,INTERVAL 1 DAY)
                        and st.cancelled = false
                        and st.type in ('BET', 'WIN', 'DIRECTWIN')
                      group by st.user_id) new
    on duplicate key update bets=bets+new.nbets, wins=wins+new.nwins;

    # user_transactions
    # BONI, RAKEBACKS,... NO PURCH and NO DEPS/PO

    insert into user_stats_daily (rdate, user_id, boni)
    select * from (
                      select statsdate as rdate,st.user_id as user_id, sum(st.credit-st.withdraw) as nboni
                      from user_transaction st
                      where st.created_at >= statsdate
                        and st.created_at < DATE_ADD(statsdate,INTERVAL 1 DAY)
                        and st.type NOT in ('PURCHASE', 'DEPOSIT', 'PAYOUT_RESERVE')
                      group by st.user_id) new
    on duplicate key update boni=boni+new.nboni;

    # insert/update deposits + fees

    if( social is true) then
        # purchases
        INSERT into user_stats_daily (rdate, user_id, depos)
        select * from (
                          select statsdate as rdate,p.user_id, sum(p.applied_cost) as ndepos
                          from purchases p
                          where p.created_at >= statsdate
                            and p.created_at <  DATE_ADD(statsdate, INTERVAL 1 DAY)
                            and p.status = 'SUCCESS'
                          group by p.user_id) new
        on duplicate key update depos=new.ndepos;
    ELSE
        # deposits
        INSERT into user_stats_daily (rdate, user_id, depos, dep_f)
        select * from (
                          select statsdate as rdate,dp.user_id, sum(dp.amount) as ndepos, sum(dp.fee) as ndep_f
                          from payment_deposit dp
                          where dp.created_at >= statsdate
                            and dp.created_at <  DATE_ADD(statsdate, INTERVAL 1 DAY)
                            and dp.status = 'SUCCESS'
                          group by dp.user_id) new
        on duplicate key update depos=new.ndepos, dep_f=new.ndep_f;
        # payouts
        INSERT into user_stats_daily (rdate, user_id, pouts, pout_f)
        select * from (
                          select statsdate as rdate,dp.user_id, sum(dp.amount) as npouts, sum(dp.fee) as npout_f
                          from payment_payout dp
                          where dp.created_at >= statsdate
                            and dp.created_at <  DATE_ADD(statsdate, INTERVAL 1 DAY)
                            and dp.status = 'SUCCESS'
                          group by dp.user_id) new
        on duplicate key update pouts=new.npouts, pout_f=new.npout_f;



    END IF;

END;