create procedure REPLACE_SESSION_RANK_ENTRY(IN created_at timestamp, IN session_id bigint)
    modifies sql data
BEGIN
    INSERT into session_ranks (created_at, session_id, maxwin, maxmult, hsaldo, user_id, game_id)
    select *
    from (select gs.start_at                    as created_at,
                 gs.session_id                  as session_id,
                 gs.max_win                     as maxwin,
                 COALESCE(gs.max_mult, 0) * 100 as maxmult,
                 ((gs.sum_win - gs.sum_bet))    as hsaldo,
                 gs.user_id                     as user_id,
                 gs.game_id                     as game_id
          from session_statistics gs
          where gs.session_id = session_id) new
    on duplicate key update maxwin=new.maxwin, maxmult=new.maxmult, hsaldo=new.hsaldo;
END;

