create procedure UPDATE_SESSION_DATA_USER_STATISTICS(IN x_user_id bigint, IN x_num_sessions int,
                                                     IN x_start_at timestamp, IN x_num_plays int,
                                                     IN x_num_wins int, IN x_sum_bet decimal(21, 4),
                                                     IN x_sum_win decimal(21, 4), IN x_sum_boost decimal(21, 4),
                                                     IN x_session_secs int)
    modifies sql data
BEGIN
    IF (x_num_plays > 0 or x_num_plays < 0) THEN
        INSERT into user_statistics (user_id, ss_session_count, ss_first_session_at, ss_last_session_at, ss_num_spins,
                                     ss_num_wins, ss_sum_bet, ss_sum_win, ss_sum_boost, ss_total_session_sec)
        values (x_user_id, x_num_sessions, x_start_at, x_start_at, x_num_plays, x_num_wins, x_sum_bet, x_sum_win,
                x_sum_boost,
                x_session_secs)
        on duplicate key update ss_session_count=ss_session_count + x_num_sessions,
                                ss_sum_win = ss_sum_win + x_sum_win,
                                ss_sum_boost=ss_sum_boost + x_sum_boost,
                                ss_sum_bet = ss_sum_bet + x_sum_bet,
                                ss_num_spins = ss_num_spins + x_num_plays,
                                ss_num_wins = ss_num_wins + x_num_wins,
                                ss_last_session_at = GREATEST(COALESCE(ss_last_session_at, '2001-01-01'), x_start_at),
                                ss_first_session_at= LEAST(COALESCE(ss_first_session_at, '2035-01-01'), x_start_at),
                                ss_total_session_sec=ss_total_session_sec + x_session_secs;
    END IF;
END;