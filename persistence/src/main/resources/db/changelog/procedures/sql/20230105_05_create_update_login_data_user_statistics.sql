CREATE PROCEDURE UPDATE_LOGIN_DATA_USER_STATISTICS(IN x_user_id BIGINT, IN x_login_ts TIMESTAMP)
    MODIFIES SQL DATA
BEGIN

    INSERT into user_statistics (user_id, first_login, last_login,
                                 logins_web, logins_ios, logins_android)
    select *
    from (select x_user_id                                                        as user_id,
                 x_login_ts                                                       as first_login,
                 x_login_ts                                                       as last_login,
                 count(distinct IF(platform = 'WEB', DATE(created_at), null))     as logins_web,
                 count(distinct IF(platform = 'IOS', DATE(created_at), null))     as logins_ios,
                 count(distinct IF(platform = 'ANDROID', DATE(created_at), null)) as logins_android
          from user_logins
          where user_id = x_user_id) new
    on duplicate key update user_statistics.first_login   = LEAST(COALESCE(user_statistics.first_login, '2035-01-01'),
                                                                  new.first_login),
                            user_statistics.last_login    = GREATEST(COALESCE(user_statistics.last_login, '2001-01-01'),
                                                                     new.last_login),
                            user_statistics.logins_web    = new.logins_web,
                            user_statistics.logins_ios    = new.logins_ios,
                            user_statistics.logins_android= new.logins_android;
END;
