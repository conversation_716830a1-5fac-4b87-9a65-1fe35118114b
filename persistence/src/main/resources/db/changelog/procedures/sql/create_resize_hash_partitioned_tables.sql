create procedure RESIZE_HASH_PARTITIONS(IN p_schema varchar(64), IN p_table varchar(64),
                                        IN new_partcount int)
BEGIN

    DECLARE current_partition_count int;
    DECLARE current_partmethod varchar(100);
    DECLARE current_partition_expression varchar(100);

    SELECT COALESCE(PARTITION_METHOD, 'UNPARTITIONED') as partmethod,
           count(*)                                    as partcount,
           PARTITION_EXPRESSION                        as partexpr
    INTO current_partmethod,current_partition_count,current_partition_expression
    FROM INFORMATION_SCHEMA.PARTITIONS
    WHERE TABLE_SCHEMA = p_schema
      AND TABLE_NAME = p_table;

    IF current_partmethod = 'HASH' and new_partcount > 0 THEN

        IF current_partition_count > new_partcount THEN
            set @s = concat('ALTER TABLE ', p_table, ' COALESCE PARTITION ', current_partition_count - new_partcount);
            prepare stmt from @s;
            execute stmt;
            deallocate prepare stmt;
        ELSE
            IF current_partition_count < new_partcount THEN

                set @s = concat('ALTER TABLE ', p_table, ' ADD PARTITION PARTITIONS ',
                                new_partcount - current_partition_count);
                prepare stmt from @s;
                execute stmt;
                deallocate prepare stmt;


            END IF;
        END IF;
    ELSE
        IF current_partmethod = 'HASH' and new_partcount = 0 THEN
            set @s = concat('ALTER TABLE ', p_table, ' REMOVE PARTITIONING');
            prepare stmt from @s;
            execute stmt;
            deallocate prepare stmt;
        end if;
    END IF;

END;
