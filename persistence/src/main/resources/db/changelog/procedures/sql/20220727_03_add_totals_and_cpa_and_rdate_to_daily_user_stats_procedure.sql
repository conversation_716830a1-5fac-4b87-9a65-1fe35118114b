create procedure REPLACE_USER_DAILY_STATS(IN statsdate DATE, IN social bit(1))
    modifies sql data
BEGIN

    # Delete entries
    DELETE from user_stats_daily where rdate = statsdate;

    # Insert bet/win stats
    # session_transaction

    insert into user_stats_daily (rdate, user_id, bets, wins, hedge, boni)
    select statsdate                                                as rdate,
           s.user_id                                                as user_id,
           sum(st.bet)                                              as bets,
           sum(st.win)                                              as wins,
           sum(st.bet * (1 - coalesce(g.rtp, gi.rtp, 100.0) / 100)) as hedge,
           sum(st.boost - st.win)                                   as bonus
    from session s
             join games g on s.game_id = g.id
             join session_transaction st
                  on s.user_id = st.user_id and s.session_id = st.session_id
             left join game_info gi on g.info_id = gi.id
    where s.created_at >= DATE_SUB(statsdate, INTERVAL 1 DAY)
      and s.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
      and st.created_at >= statsdate
      and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
      and st.cancelled = false
      and st.type in ('BET', 'WIN', 'DIRECTWIN')
    group by s.user_id;

    # ctransactions

    insert into user_stats_daily (rdate, user_id, bets, wins, hedge, boni)
    select *
    from (select statsdate                                                as rdate,
                 st.user_id                                               as user_id,
                 sum(st.bet)                                              as nbets,
                 sum(st.win)                                              as nwins,
                 sum(st.bet * (1 - coalesce(g.rtp, gi.rtp, 100.0) / 100)) as nhedge,
                 sum(st.boost - st.win)                                   as bonus
          from ctransactions st
                   join games g on st.game_id = g.id
                   left join game_info gi on g.info_id = gi.id
          where st.created_at >= statsdate
            and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
            and st.cancelled = false
            and st.type in ('BET', 'WIN', 'DIRECTWIN')
          group by st.user_id) new
    on duplicate key update bets=bets + new.nbets,
                            wins=wins + new.nwins,
                            hedge=hedge + new.nhedge,
                            boni=boni + new.bonus;

    # transactions
    insert into user_stats_daily (rdate, user_id, bets, wins, hedge, boni)
    select *
    from (select statsdate                                                as rdate,
                 st.user_id                                               as user_id,
                 sum(st.bet)                                              as nbets,
                 sum(st.win)                                              as nwins,
                 sum(st.bet * (1 - coalesce(g.rtp, gi.rtp, 100.0) / 100)) as nhedge,
                 sum(st.boost - st.win)                                   as bonus
          from transactions st
                   join games g on st.game_id = g.id
                   left join game_info gi on g.info_id = gi.id
          where st.created_at >= statsdate
            and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
            and st.cancelled = false
            and st.type in ('BET', 'WIN', 'DIRECTWIN')
          group by st.user_id) new
    on duplicate key update bets=bets + new.nbets, wins=wins + new.nwins, hedge=hedge + nhedge, boni=boni + new.bonus;

    # user_transactions
    # BONI, RAKEBACKS,... NO PURCH and NO DEPS/PO

    insert into user_stats_daily (rdate, user_id, boni)
    select *
    from (select statsdate as rdate, st.user_id as user_id, sum(st.credit - st.withdraw) as nboni
          from user_transaction st
          where st.created_at >= statsdate
            and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
            and st.type NOT in ('PURCHASE', 'DEPOSIT', 'PAYOUT_RESERVE', 'PAYOUT_CANCEL')
          group by st.user_id) new
    on duplicate key update boni=boni + new.nboni;

    # insert/update deposits + fees

    if (social is true) then
        # purchases

        INSERT into user_stats_daily (rdate, user_id, depos)
        select *
        from (select statsdate as rdate, p.user_id, sum(p.applied_cost) as ndepos
              from purchases p
              where p.created_at >= statsdate
                and p.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and p.status = 'SUCCESS'
              group by p.user_id) new
        on duplicate key update depos=new.ndepos;


        INSERT into user_stats_daily (rdate, user_id, depos_total)
        select *
        from (select statsdate as rdate, p.user_id, ROUND(coalesce(sum(p.applied_cost), 0), 4) as ndeposTa
              from purchases p
                       join user_stats_daily usd on p.user_id = usd.user_id and usd.rdate = statsdate
              where p.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and p.status = 'SUCCESS'
              group by p.user_id) new
        on duplicate key update depos_total=new.ndeposTa;

    ELSE

        # deposits (daily)
        INSERT into user_stats_daily (rdate, user_id, depos, dep_f)
        select *
        from (select statsdate as rdate, dp.user_id, ROUND(sum(dp.amount), 4) as ndepos, sum(dp.fee) as ndep_f
              from payment_deposit dp
              where dp.created_at >= statsdate
                and dp.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and dp.status = 'SUCCESS'
              group by dp.user_id) new
        on duplicate key update depos=new.ndepos, dep_f=new.ndep_f;

        # payouts
        INSERT into user_stats_daily (rdate, user_id, pouts, pout_f)
        select *
        from (select statsdate as rdate, dp.user_id, sum(dp.amount) as npouts, sum(dp.fee) as npout_f
              from payment_payout dp
              where dp.created_at >= statsdate
                and dp.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and dp.status = 'SUCCESS'
              group by dp.user_id) new
        on duplicate key update pouts=new.npouts, pout_f=new.npout_f;

        # deposits (all)
        INSERT into user_stats_daily (rdate, user_id, depos_total)
        select *
        from (select statsdate as rdate, dp.user_id, ROUND(coalesce(sum(dp.amount), 0), 4) as ndeposT
              from payment_deposit dp
                       join user_stats_daily usd on dp.user_id = usd.user_id and usd.rdate = statsdate
              where dp.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and dp.status = 'SUCCESS'
              group by dp.user_id) new
        on duplicate key update depos_total=new.ndeposT;

        # payouts (all)
        INSERT into user_stats_daily (rdate, user_id, pouts_total)
        select *
        from (select statsdate as rdate, dp.user_id, ROUND(coalesce(sum(dp.amount), 0), 4) as npoutsT
              from payment_payout dp
                       join user_stats_daily usd on dp.user_id = usd.user_id and usd.rdate = statsdate
              where dp.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and dp.status = 'SUCCESS'
              group by dp.user_id) new
        on duplicate key update pouts_total=new.npoutsT;


    END IF;

    ## update eff. campaign history rdate
    update user_stats_daily usd join
        (select usd.rdate, usd.user_id, max(ach.rdate) as achRD
         from user_stats_daily usd
                  join aff_links al on usd.rdate = statsdate and usd.user_id = al.ref_id
                  join aff_campaigns_history ach on al.num = ach.num and al.user_id = ach.user_id
         where ach.rdate >= DATE(al.created_at)
           and ach.rdate < DATE_ADD(statsdate, INTERVAL 1 DAY)
         group by usd.rdate, usd.user_id) achrd on usd.rdate = achrd.rdate and usd.user_id = achrd.user_id
    set usd.cmp_rdate=achrd.achRD;

    ## update aff_link, set cpa hit

    update aff_links afl join (select al.user_id, al.num, al.ref_id
                               from aff_links al
                                        join user_stats_daily usd on al.ref_id = usd.user_id
                               where usd.rdate = statsdate
                                 and al.cpa_baseline <= usd.depos_total
                                 and al.cpa_hit_at is null
                               group by al.user_id, al.num, al.ref_id) xxx on afl.user_id = xxx.user_id and
                                                                              afl.num = xxx.num and
                                                                              afl.ref_id = xxx.ref_id
    set cpa_hit_at=statsdate;


END;