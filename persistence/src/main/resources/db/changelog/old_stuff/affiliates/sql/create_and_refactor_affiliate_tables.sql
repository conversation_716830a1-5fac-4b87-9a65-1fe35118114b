CREATE TABLE `affiliates2`
(
    `id`         int                                    NOT NULL AUTO_INCREMENT,
    `created_at` datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `city`       varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `country`    varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `email`      varchar(120) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `mobile`     varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `name`       varchar(180) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `secretCode` varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `status`     varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
    `street`     varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `zipcode`    varchar(10) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into affiliates2
select *
from affiliates;

RENAME TABLE affiliates to affiliates_tmp, affiliates2 to affiliates;

DROP TABLE affiliates_tmp;

CREATE TABLE `bonuscodes2`
(
    `id`          int                                     NOT NULL AUTO_INCREMENT,
    `created_at`  datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `active`      bit(1)                                  NOT NULL DEFAULT true,
    `aff_id`      int                                     NOT NULL,
    `bonuscode`   varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `maxusage`    int(11)                                 NOT NULL DEFAULT 10000,
    `pricedef`    varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
    `type`        varchar(30) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT 'SHARED',
    `usedcount`   int(11)                                 NOT NULL DEFAULT 0,
    `valid_from`  datetime                                NULL     DEFAULT NULL,
    `valid_until` datetime                                NULL     DEFAULT NULL,
    `version`     int                                     NOT NULL DEFAULT 1,
    `user_id`     bigint(20)                              NULL     DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UK_bonuscode_code` (`bonuscode`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2000
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into bonuscodes2
select *
from bonuscodes;

RENAME TABLE bonuscodes to bonuscodes_tmp, bonuscodes2 to bonuscodes;

DROP TABLE bonuscodes_tmp;


create table user_campaign_link
(
    user_id     bigint    not null,
    campaign_id int       not null,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    INDEX idx_cmpgn_id (`campaign_id`, `created_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

create table user_bonuscode_link
(
    user_id      bigint    not null,
    bonuscode_id int       not null,
    created_at   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    INDEX idx_cmpgn_id (`bonuscode_id`, `created_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

