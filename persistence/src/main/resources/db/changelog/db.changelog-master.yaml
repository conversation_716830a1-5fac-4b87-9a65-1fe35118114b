databaseChangeLog:

  - include:
      file: db/changelog/schema/base_schema.yaml

  - include:
      file: db/changelog/schema/base_views.yaml

  - include:
      file: db/changelog/schema/base_triggers.yaml

  - include:
      file: db/changelog/schema/base_procedures.yaml

  - include:
      file: db/changelog/schema/permissions_and_roles.yaml

  - include:
      file: db/changelog/schema/unittest_data.yaml

  - include:
      file: db/changelog/auth/add_data_to_user_verification.yaml

  - includeAll:
      path: db/changelog/social-common
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/payment
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/coinspaid
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/doubleup
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/missions
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/wheelspins
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/bonuscode
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/useraudit
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/userexclusion
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/issuemanager
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/game/change_gameview.yaml

  - include:
      file: db/changelog/wallet/add_open_wager_to_wallet.yaml

  - include:
      file: db/changelog/auth/user_mail_add_reftk_idx.yaml

  - include:
      file: db/changelog/user/20220304_01_add_user_message_table.yaml

  - include:
      file: db/changelog/session/20220309_01_add_session_createdat_gameid_index.yaml

  - include:
      file: db/changelog/game/add_infos_to_integrations.yaml

  - include:
      file: db/changelog/user/20220310_02_add_fcmtoken_index_userlogin.yaml

  - include:
      file: db/changelog/game/drop_dupl_index_game_info_content.yaml

  - include:
      file: db/changelog/user/20220317_02_add_user_tag_tables.yaml

  - include:
      file: db/changelog/user/20220317_03_add_tags_to_user_view.yaml

  - include:
      file: db/changelog/user/20220317_04_remove_tags_from_users_table.yaml

  - include:
      file: db/changelog/user/20220321_01_set_autoincr_value_on_user_tag.yaml

  - include:
      file: db/changelog/procedures/20220321_01_add_resize_hash_partitions_procedure.yaml

  - include:
      file: db/changelog/social/add_active_flag_to_consumable.yaml

  - include:
      file: db/changelog/affiliates/add_name_and_cutatt_to_aff_tables.yaml

  - include:
      file: db/changelog/social/purchases_add_id_idx.yaml

  - include:
      file: db/changelog/user/20220325_01_add_user_daily_stats_table.yaml

  - include:
      file: db/changelog/procedures/20220325_02_add_create_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/social/20220325_03_purchases_add_created_at_idx.yaml

  - include:
      file: db/changelog/user/20220325_04_add_created_at_index_on_usertransaction.yaml

  - include:
      file: db/changelog/affiliates/change_cutat_to_date_set_def_values.yaml

  - include:
      file: db/changelog/session/20220329_01_recreate_session_rank_table.yaml

  - include:
      file: db/changelog/user/20220331_02_add_hedge_effrtp_user_daily_stats_table.yaml

  - include:
      file: db/changelog/procedures/20220331_01_add_hedge_to_create_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/affiliates/20220406_add_revshares_drop_awarded_to_campaign_table.yaml

  - include:
      file: db/changelog/affiliates/20220406_campaign_history_table_and_trigger.yaml

  - include:
      file: db/changelog/affiliates/20220407_campaign_statement_table.yaml

  - include:
      file: db/changelog/game/change_game_rank_daily_table.yaml

  - include:
      file: db/changelog/affiliates/add_info_json_to_aff_statement_table.yaml

  - include:
      file: db/changelog/config/20220411_add_config_property_table.yaml

  - include:
      file: db/changelog/game/add_rtp_to_game.yaml

  #  - include:
  #      file: db/changelog/bonus/20220413_03_add_user_rakebacks_table.yaml

  - include:
      file: db/changelog/social/20220419_01_purchases_add_psp_idx.yaml

  - includeAll:
      path: db/changelog/ranking
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/affiliates/20220512_campaign_history_table_and_trigger2.yaml

  - include:
      file: db/changelog/game/change_gameview_rtp.yaml

  - include:
      file: db/changelog/user/20220615_01_add_user_utm_table.yaml

  - includeAll:
      path: db/changelog/fingerprint
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/bonus/20220701_01_add_user_freespins_table.yaml

  - include:
      file: db/changelog/session/20220701_01_session_add_betlevel.yaml

  - include:
      file: db/changelog/game/2022070101_activegames_add_betlevel.yaml

  - include:
      file: db/changelog/game/2022070102_vendors_add_betlevel.yaml

  - include:
      file: db/changelog/game/2022070401_change_gameview_and_vendor_search_betLevels.yaml

  - include:
      file: db/changelog/game/2022070402_games_add_level.yaml

  - include:
      file: db/changelog/user/20220705_01_add_unlocks_to_usergameattributes.yaml

  - includeAll:
      path: db/changelog/notification-email
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20220708_01_add_expire_at_to_usermessages.yaml

  - include:
      file: db/changelog/user/20220708_02_change_localisation_template_type.yaml

  - include:
      file: db/changelog/bonus/20220708_01_add_user_promotions_table.yaml

  - include:
      file: db/changelog/localisation/add_type_to_localised.yaml

  - include:
      file: db/changelog/social/20220720_01_add_parameters_to_level.yaml

  - include:
      file: db/changelog/social/20220722_01_add_spins_to_level.yaml

  - include:
      file: db/changelog/game/20220725_01_extend_description_columns_game_info.yaml

  - include:
      file: db/changelog/bonus/20220721_add_deposit_id_to_freespin_promotion_claims.yaml

  - include:
      file: db/changelog/procedures/20220725_02_refactor_bonus_calc_in_create_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/bonus/20220726_01_rebuild_user_freespins_table_PK.yaml

  - include:
      file: db/changelog/affiliates/20220727_campaign_history_update_trigger_2.yaml

  - include:
      file: db/changelog/user/20220727_02_add_payment_totals_and_rdate_to_user_daily_stats.yaml

  - include:
      file: db/changelog/procedures/20220727_03_add_totals_cmprdate_to_create_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/procedures/20220727_04_fix_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/procedures/20220728_01_add_entry_when_linked_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/game/2022080103_change_gameview_add_gameRtp_unlocklevel.yaml

  - include:
      file: db/changelog/game/20220801_01_add_game_tag_tables.yaml

  - include:
      file: db/changelog/bonus/20220811_add_extid_played_bet_win_to_user_freespins_table.yaml

  - include:
      file: db/changelog/bonus/20220811_add_fs_cols_to_user_daily_stats_table.yaml

  - include:
      file: db/changelog/procedures/20220811_03_add_freespin_updates_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/auth/20220812_01_add_user_rtokens_table.yaml

  - include:
      file: db/changelog/affiliates/20220811_add_booked_by_to_stmt.yaml

  - include:
      file: db/changelog/game/2022081901_games_set_bools_to_non_null.yaml

  - include:
      file: db/changelog/wallet/20220822_01_refactor_wallets_table.yaml

  - include:
      file: db/changelog/session/20220822_02_recreate_transactions_table.yaml

  - include:
      file: db/changelog/session/20220822_03_session_transaction_add_bonus_stuff.yaml

  - include:
      file: db/changelog/user/20220822_04_user_transaction_add_bonus_stuff.yaml

  - include:
      file: db/changelog/jurisdiction/2022082601_create_jd_countries_table.yaml

  - include:
      file: db/changelog/bonus/20220826_02_rebuild_freespin_promotion_claim_table_PK.yaml

  - includeAll:
      path: db/changelog/kyc
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20220830_04_add_expire_at_idx_usermessages.yaml

  - include:
      file: db/changelog/bonus/20220830_rebuild_promotions.yaml

  - include:
      file: db/changelog/procedures/20220906_06_refactor_daily_user_stats_procedure.yaml

  - include:
      file: db/changelog/session/20220909_01_session_transaction_archive_table.yaml

  - include:
      file: db/changelog/session/20220909_02_session_add_bonus_flag.yaml

  - include:
      file: db/changelog/session/20220909_03_add_session_task_and_ext_tables.yaml

  - include:
      file: db/changelog/session/20220909_04_create_archive_session_procedure.yaml

  - include:
      file: db/changelog/session/20220912_01_add_session_session_id_index.yaml

  - include:
      file: db/changelog/session/20220912_02_recreate_proc_archive_transactions.yaml

  - include:
      file: db/changelog/resource/20221014_01_add_resource_tables.yaml

  - include:
      file: db/changelog/session/20221019_01_change_maxwin_maxmult_session_ranks.yaml

  - include:
      file: db/changelog/user/20221019_02_user_table_add_country.yaml

  - include:
      file: db/changelog/user/20221021_01_extend_user_statistics.yaml

  - include:
      file: db/changelog/procedures/20221021_02_add_logins_stats_update_login_data_procedure.yaml

  - include:
      file: db/changelog/user/20221021_03_update_view_logins_purch_from_statistics.yaml

  - include:
      file: db/changelog/user/20221021_04_extend_user_statistics_tags.yaml

  - include:
      file: db/changelog/user/20221021_05_update_view_tags.yaml

  - include:
      file: db/changelog/stats/20221019_01_add_stats_daily_table.yaml

  - include:
      file: db/changelog/procedures/20221019_01_add_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/bonus/20221029_rebuild_promotions_table.yaml

  - include:
      file: db/changelog/bonus/20221031_create_promotion_user_link_table.yaml

  - include:
      file: db/changelog/bonus/20221106_add_claimid_to_bonus.yaml

  - include:
      file: db/changelog/procedures/20221021_03_update_create_stats_daily_procedure.yaml

  - includeAll:
      path: db/changelog/leaderboards
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20221103_01_user_table_add_ext_id.yaml

  - include:
      file: db/changelog/user/20221104_01_user_table_add_extid.yaml

  - include:
      file: db/changelog/bonus/20221109_01_add_zero_out_and_max_cashout_to_bonus.yaml

  - include:
      file: db/changelog/bonus/20221109_02_update_bonus_table.yaml

  - include:
      file: db/changelog/session/20221110_01_add_sum_bbet_bwin_to_game_statistics.yaml

  - include:
      file: db/changelog/user/20221110_02_add_sum_bbet_bwin_to_user_stats_daily.yaml

  - include:
      file: db/changelog/procedures/20221110_03_refactor_daily_user_stats_procedure_add_bonus_vals.yaml

  - include:
      file: db/changelog/user/20221110_04_add_sum_bbet_bwin_to_user_statistics.yaml

  - include:
      file: db/changelog/session/20221110_05_recreate_game_statistics_trigger.yaml

  - include:
      file: db/changelog/procedures/20221111_01_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/bonus/20221111_02_add_status_expiry_index_on_user_bonus.yaml

  - include:
      file: db/changelog/bonus/20221206_add_createdBy_to_bonus_claims.yaml

  - include:
      file: db/changelog/maintenance/20221220-01-add_duration_to_maintenance_events.yaml

  - includeAll:
      path: db/changelog/crm-customerio
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/procedures/20221228_02_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/procedures/20230103_01_create_tx_recreateUserDailyStats.yaml

  - include:
      file: db/changelog/user/20230105_01_add_last_login_index_user_statistics.yaml

  - include:
      file: db/changelog/user/20230105_02_add_user_logins_tables.yaml

  - include:
      file: db/changelog/procedures/20230105_04_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/procedures/20230105_05_add_logins_stats_update_logins_data_procedure.yaml

  - include:
      file: db/changelog/user/20230105_06_add_triggers_user_logins.yaml

  - include:
      file: db/changelog/session/20230110_01_add_start_balance_fields_to_session_and_activegames.yaml

  - include:
      file: db/changelog/session/20230110_02_add_new_sessionstats_tables.yaml

  - include:
      file: db/changelog/session/20230110_03_add_triggers_new_session_stats.yaml

  - include:
      file: db/changelog/procedures/20230116_01_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/procedures/20230116_02_create_tx_recreateUserDailyStats.yaml

  - include:
      file: db/changelog/session/20230116_03_add_triggers_new_session_stats.yaml

  - include:
      file: db/changelog/procedures/20230116_04_create_sessionRankUpdate.yaml

  - include:
      file: db/changelog/procedures/20230116_05_create_userStatsUpdateTrigger.yaml

  - include:
      file: db/changelog/session/20230127-01-add_bonusid_to_session.yaml

  - include:
      file: db/changelog/game/20230130_01-add_scope_to_game_tag.yaml

  - include:
      file: db/changelog/procedures/20230131_01_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/procedures/20230131_02_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/game/20230201_01_add_key_changed_at_to_game.yaml

  - include:
      file: db/changelog/game/20230201_02_change_gameview_add_key_changed_at.yaml

  - include:
      file: db/changelog/game/20230202_01_change_gameview_add_isvalid.yaml

  - include:
      file: db/changelog/game/20230202_02_change_gameview_add_tags.yaml

  - include:
      file: db/changelog/game/20230202_03_change_gameview_add_tags.yaml

  - include:
      file: db/changelog/game/20230203_01_change_gameview_add_tags.yaml

  - include:
      file: db/changelog/bonus/20230206_create_promotion_user_links_archive.yaml

  - include:
      file: db/changelog/bonus/20230206_add_promotions_slug.yaml

  - include:
      file: db/changelog/bonus/20230206_dropMaxCount_enabledOnBonusCount.yaml

  - include:
      file: db/changelog/user/20230228_01_refact_trigger_user_logins.yaml

  - includeAll:
      path: db/changelog/postbacks
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20230320_01_update_view_join_stats.yaml

  - include:
      file: db/changelog/game/20230330_01_add_genre_type_jpmode_to_games.yaml

  - include:
      file: db/changelog/game/20230330_02_change_gameview_add_genre_type_jackpotmode.yaml

  - includeAll:
      path: db/changelog/rewards-cashback
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/rewards-rakeback
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/monitor_user_wager
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/milestones
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/social/20230510_01_add_consumables_custom_table.yaml

  - include:
      file: db/changelog/social/20230512_01_add_consumables_custom_archive_table.yaml

  - include:
      file: db/changelog/social/20230515_01_add_campaign_to_consumables_custom.yaml

  - include:
      file: db/changelog/cleanup/20230515_02_drop_obsolete_indices_p1.yaml

  - include:
      file: db/changelog/session/20230515_05_add_user_transaction_created_at_index.yaml

  - include:
      file: db/changelog/procedures/20230517_01_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/procedures/20230517_02_update_create_stats_daily_procedure.yaml

  - include:
      file: db/changelog/session/20230601-02-add_s_created_at_to_session_task.yaml

  - include:
      file: db/changelog/session/20230601_03_drop_and_add_idx_to_session_task.yaml

  - includeAll:
      path: db/changelog/rewards-tickets
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/firebase-auth
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20230622_01_update_view_no_0_as_instant.yaml

  - includeAll:
      path: db/changelog/videoads
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/session/20230731_01_add_boost_to_stats_tables.yaml

  - include:
      file: db/changelog/procedures/20230731_02_update_procedures_add_boost.yaml

  - include:
      file: db/changelog/session/20230731_03_add_triggers_new_session_stats_with_boost.yaml

  - include:
      file: db/changelog/procedures/20230801_01_fix_boost_col_error_user_daily_stats.yaml

  - include:
      file: db/changelog/user/20230810_01_remove_fcmtoken_from_userlogin.yaml


  - includeAll:
      path: db/changelog/collectibles
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/user/20231017_02_set_charsets_fix.yaml

  - includeAll:
      path: db/changelog/cpopups
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/stats/20231030_01_add_user_tx_agg_table.yaml

  - includeAll:
      path: /db/changelog/marketingemails
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/session/20250417_01_add_session_created_at_status_index.yaml

  - include:
      file: db/changelog/user/20240422_01_update_view_no_0_as_instant.yaml

  - include:
      file: db/changelog/user/20240422_02_update_view_level_plus_1_instant.yaml

  - include:
      file: db/changelog/social/20240610_01_extend_consumable_assets.yaml

  - include:
      file: db/changelog/cash_removal/20240709_set_missing_defaults.yaml

  - include:
      file: db/changelog/cash_removal/20240716_01_update_archival_procedure.yaml

  - include:
      file: db/changelog/cash_removal/20240716_02_update_stats_daily_procedure.yaml

  - include:
      file: db/changelog/cash_removal/20240717_01_update_archive_transactions_procedure.yaml

  - includeAll:
      path: db/changelog/appsflyer
      errorIfMissingOrEmpty: false

  - include:
      file: db/changelog/cash_removal/20241101_01_update_archival_procedure.yaml

  - include:
      file: db/changelog/cash_removal/20241112_01_remove_cash_columns_session_transaction_archive.yaml

  - include:
      file: db/changelog/cash_removal/20241112_02_remove_cash_columns_session_and_activegames.yaml

  - includeAll:
      path: db/changelog/jackpots
      errorIfMissingOrEmpty: false

  - includeAll:
      path: db/changelog/safes
      errorIfMissingOrEmpty: false