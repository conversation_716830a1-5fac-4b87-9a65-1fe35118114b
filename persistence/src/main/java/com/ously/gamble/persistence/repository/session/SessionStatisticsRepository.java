package com.ously.gamble.persistence.repository.session;

import com.ously.gamble.persistence.dto.SessionStatDto;
import com.ously.gamble.persistence.dto.SessionTxDto;
import com.ously.gamble.persistence.model.session.SessionStatisticsDao;
import com.ously.gamble.persistence.model.session.SessionStatisticsRD;
import com.ously.gamble.persistence.projections.GameRtpPJ;
import com.ously.gamble.persistence.projections.LeaderboardEntryPJ;
import com.ously.gamble.persistence.projections.UserSessionInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SessionStatisticsRepository extends JpaRepository<SessionStatisticsDao, Long> {


    @Query(nativeQuery = true)
    List<SessionTxDto> getSessionTransactions(long userId, long sessionId);

    @Query(nativeQuery = true)
    List<SessionTxDto> getUsersRounds(long userId, long startpos, long size);


    @Query(nativeQuery = true)
    List<SessionStatDto> getSessionStatsDto(long userId, long startpos, long size);

    @Query(nativeQuery = true, value = """
                 select g.name             as gameName,
                  g.id               as gameId,
                  v.vendor_name      as vendorName,
                  gs.rtp             as rtp,
                  gs.spins           as spins,
                  gs.wins            as wins,
                  gs.saldo           as saldo,
                  gs.sumBets         as sumBets,
                  gs.sumWins         as sumWins,
                  gs.maxwin          as maxwin,
                  gs.maxMultiplier   as maxMultiplier,
                  gs.sessions        as sessions,
                  gi.id              as slotCatalogId,
                  v.slotcatalog_name as slotCatalogVendorName
            from games g
                    left join game_info gi on g.info_id = gi.id
                    join vendors v on g.vendor_id = v.id
                    join
                (select gs.game_id                        as gameId,
                        sum(gs.sum_win) / sum(gs.sum_bet) as rtp,
                        sum(gs.num_bets)                 as spins,
                        sum(gs.num_wins)                  as wins,
                        sum(gs.sum_win - gs.sum_bet)/100      as saldo,
                        sum(gs.sum_bet)/100                   as sumBets,
                        sum(gs.sum_win)/100                   as sumWins,
                        max(gs.max_win)/100                   as maxwin,
                        max(gs.max_mult)            as maxMultiplier,
                        count(*)                          as sessions
                 from session_statistics_lb sj join session_statistics gs on sj.session_id=gs.session_id
                 where sj.rdate  >= DATE(?1)
                   and sj.rdate < DATE(?2)
                 group by gs.game_id) gs on g.id = gs.gameId
            where g.active IS true
             and v.active IS true
            order by spins desc
            """)
    List<GameRtpPJ> getGameRtpsFromTo(LocalDateTime from, LocalDateTime to);

    @Query(nativeQuery = true,
            value = """
                    select
                    UNIX_TIMESTAMP(gss.start_at) as startAt,
                    UNIX_TIMESTAMP(gss.end_at) as endAt,g.name as gameName,v.`vendor_name` as vendorName,
                    gss.max_bet/100 as maxBet,gss.max_win/100 as maxWin,gss.num_bets as numSpins,IFNULL(gss.max_mult,0.0) as maxMultiplier,
                    gss.sum_bet/100 as sumBet,gss.sum_win/100 as sumWin,
                    gss.session_id as sessionId, g.id as gameId,
                    s.start_balance as startBal, s.start_balance - gss.sum_bet/100 + gss.sum_win/100 as endBal
                    from session s join session_statistics gss on s.session_id = gss.session_id join games g
                    on gss.game_id = g.id join vendors v on g.vendor_id = v.id
                    where s.user_id = ?1 order by s.created_at desc LIMIT ?2,?3
                    """)
    List<UserSessionInfo> getUserSessionInfosPaged(long userId, long startpos, long size);

    @Query(nativeQuery = true, value = """
            select coalesce(sum(ss.num_bets),0) from session s join session_statistics ss on s.session_id=ss.session_id
                                                                                       where s.user_id=?1
            """)
    long getUserRoundCount(long userId);

    @Query(nativeQuery = true, value = "select coalesce(count(*),0) from session s join session_statistics ss on s.session_id=ss.session_id where s.user_id = ?1")
    Long getUserSessionInfosCount(long userId);

    @Query(nativeQuery = true, value = "select coalesce(count(*),0) from session s where s.user_id = ?1")
    long getUserSessionsCount(long userId);

    @Query(nativeQuery = true, value = "select CONCAT(user_id,'-',GROUP_CONCAT(s.session_id)) as sessions from session s where s.created_at >= ?1 and s.created_at < DATE_ADD(?1, INTERVAL 1 DAY) group by user_id ")
    List<String> getSessionsGroupedByUser(LocalDate day);

    /**
     * aggregates session statistics over session_transaction table
     *
     * @param sessionId sessionId
     * @param userId    userId
     * @return list of RD entries (one per day)
     */
    @Query(nativeQuery = true)
    List<SessionStatisticsRD> aggregateSessionDays(long sessionId, long userId);

    /**
     * aggregates session statistics over session_transaction_archive table
     *
     * @param sessionId sessionId
     * @param userId    userId
     * @return list of RD entries (one per day)
     */
    @Query(nativeQuery = true)
    List<SessionStatisticsRD> aggregateArchivedSessionDays(long sessionId, long userId);

    /**
     * aggregates session statistics over transactions/ctransactions tables
     *
     * @param sessionId sessionId
     * @param userId    userId
     * @return list of RD entries (one per day)
     */
    @Query(nativeQuery = true)
    List<SessionStatisticsRD> aggregateLegacySessionDays(long sessionId, long userId);


    @Modifying
    @Query(nativeQuery = true, value = "delete from session_statistics_rd where session_id=?1")
    int deleteSessionStatisticsRD(long sessionId);

    @Modifying
    @Query(nativeQuery = true, value = "delete from session_statistics where session_id=?1")
    int deleteSessionStatisticsEntry(long sessionId);

    @Query(nativeQuery = true, value = """
            select
                    CONCAT_WS(',',
                        user_id,game_id,
                        UNIX_TIMESTAMP(CONVERT_TZ(start_at, '+00:00', 'SYSTEM')),
                        UNIX_TIMESTAMP(CONVERT_TZ(end_at, '+00:00', 'SYSTEM')),
                        num_bets,num_wins,0,0,
                        sum_bet,sum_win,0,0,
                        max_bet,max_win,0,0,
                        FLOOR(max_mult * 100),0
                     ) as chksum
            from  session_statistics where session_id = ?1
            """)
    Optional<String> getChecksumForSessionStatisticsInDB(long sessionId);

    @Query(nativeQuery = true, value = """
            select
            sr.user_id as userId,
            sr.maxwin as value,
            u.display_name     as dispName,
            sr.created_at as valueDate,
            g.name             as gameName,
            sr.game_id          as gameId,
            v.vendor_name      as providerName
                    from
            session_ranks sr
            join users u on sr.user_id = u.id
            join games g on sr.game_id = g.id
            join vendors v on g.vendor_id = v.id
                    where
            sr.created_at > TIMESTAMPADD(DAY, -1 * ?1, CURRENT_TIMESTAMP)
            order by sr.maxwin desc
            limit ?2""")
    List<LeaderboardEntryPJ> getTopMaxwinsForDaysWithLimitNew(int days, int limit);


    @Query(nativeQuery = true, value = """
            select
            sr.user_id as userId,
            sr.maxmult as value,
            u.display_name     as dispName,
            sr.created_at as valueDate,
            g.name             as gameName,
            sr.game_id          as gameId,
            v.vendor_name      as providerName
                    from
            session_ranks sr
            join users u on sr.user_id = u.id
            join games g on sr.game_id = g.id
            join vendors v on g.vendor_id = v.id
                    where
            sr.created_at > TIMESTAMPADD(DAY, -1 * ?1, CURRENT_TIMESTAMP)
            order by sr.maxmult desc
            limit ?2""")
    List<LeaderboardEntryPJ> getTopMultipliersForDaysWithLimitNew(int days, int limit);

    @Query(nativeQuery = true, value = """
             select
            sr.user_id as userId,
            sr.hsaldo as value,
            u.display_name     as dispName,
            sr.created_at as valueDate,
            g.name             as gameName,
            sr.game_id          as gameId,
            v.vendor_name      as providerName
                    from
            session_ranks sr
                    join users u on sr.user_id = u.id
                    join games g on sr.game_id = g.id
                    join vendors v on g.vendor_id = v.id
                            where
                    sr.created_at > TIMESTAMPADD(DAY, -1 * ?1, CURRENT_TIMESTAMP)
                    order by sr.hsaldo desc
                    limit ?2""")
    List<LeaderboardEntryPJ> getTopProfitsForDaysWithLimitNew(int days, int limit);
}