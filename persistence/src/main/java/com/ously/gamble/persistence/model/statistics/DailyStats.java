package com.ously.gamble.persistence.model.statistics;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Table(name = "stats_daily")
@EntityListeners(AuditingEntityListener.class)
public class DailyStats implements Persistable<LocalDate>, Serializable {

    @Transient
    @JsonIgnore
    private boolean isNew = true;

    @Id
    @Column(name = "rdate")
    LocalDate rDate;



    @Column(name = "deposits_sum")
    BigDecimal depositsSum;

    @Column(name = "deposits_count")
    Integer depositsCount;

    @Column(name = "deposits_user_count")
    Integer depositsUserCount;

    /**
     * First time depositors
     */
    @Column(name = "ftds")
    Integer ftds;

    @Column(name = "active_players")
    Integer activePlayers;

    @Column(name = "sign_ups")
    Integer signUps;


    @Column(name = "bets_sum")
    BigDecimal betsSum;

    @Column(name = "bets_count")
    Integer betsCount;

    @Column(name = "wins_sum")
    BigDecimal winsSum;

    @Column(name = "wins_count")
    Integer winsCount;


    @Override
    @JsonIgnore
    public LocalDate getId() {
        return this.rDate;
    }

    @Override
    @Transient
    @JsonIgnore
    public boolean isNew() {
        return this.isNew;
    }

    @PostLoad
    @PrePersist
    void trackNotNew() {
        this.isNew = false;
    }

    public LocalDate getrDate() {
        return rDate;
    }


    public BigDecimal getDepositsSum() {
        return depositsSum;
    }

    public Integer getDepositsCount() {
        return depositsCount;
    }

    public Integer getDepositsUserCount() {
        return depositsUserCount;
    }

    public Integer getFtds() {
        return ftds;
    }

    public Integer getActivePlayers() {
        return activePlayers;
    }

    public Integer getSignUps() {
        return signUps;
    }


    public BigDecimal getBetsSum() {
        return betsSum;
    }

    public Integer getBetsCount() {
        return betsCount;
    }

    public BigDecimal getWinsSum() {
        return winsSum;
    }

    public Integer getWinsCount() {
        return winsCount;
    }

}
