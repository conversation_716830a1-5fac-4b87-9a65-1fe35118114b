package com.ously.gamble.persistence.db.configuration;


import com.ously.gamble.persistence.db.routingds.DbType;
import com.ously.gamble.persistence.db.routingds.RoutingDataSource;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseDataSource;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import javax.sql.DataSource;
import java.util.EnumMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class RoutingDatasourceConfiguration {

    final Logger log = LoggerFactory.getLogger(RoutingDatasourceConfiguration.class);

    @Value("${spring.liquibase.user:root}")
    private String lb_username;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.readUrl:}")
    private String readUrl;

    @Value("${spring.datasource.hikari.connectionTimeout:15000}")
    Long hConnTimeout;
    @Value("${spring.datasource.hikari.idleTimeout:180000}")
    Long hIdleTimeout;
    @Value("${spring.datasource.hikari.maxLifetime:3600000}")
    Long hMaxLifetime;
    @Value("${spring.datasource.hikari.maximum-pool-size:10}")
    Long hMaxPoolSize;
    @Value("${spring.datasource.hikari.leak-detection-threshold:300000}")
    Long hLeakTreshold;
    @Value("${spring.datasource.hikari.minimum-idle:2}")
    Long hMinIdle;
    @Value("${spring.datasource.hikari.auto-commit:false}")
    Boolean hAutoCommit = Boolean.FALSE;

    @Value("${spring.datasource.hikari.exception-override-class-name:}")
    String exceptionOverrideClassname;


    @LiquibaseDataSource
    @Bean
    public DataSource liquibaseDataSource() {
//        LiquibaseConfiguration.getInstance().getConfiguration(HubConfiguration.class).setLiquibaseHubMode("OFF");

        var dsb = DataSourceBuilder.create()
                .username(getLiquibaseUsername())
                .password(password)
                .url(url);
        if (url.startsWith("jdbc:aws-wrapper:mysql:")) {
            dsb.driverClassName("software.amazon.jdbc.Driver");
        }
        var ds = dsb.build();
        if (ds instanceof HikariDataSource) {
            ((HikariDataSource) ds).setMinimumIdle(0);
            ((HikariDataSource) ds).setMaximumPoolSize(2);
            ((HikariDataSource) ds).setPoolName("Liquibase Pool");
        }
        return ds;
    }

    @Bean
    public DataSourceHealthIndicator mainDB() {
        return new DataSourceHealthIndicator(mainDataSource());
    }

    @Bean(destroyMethod = "close")
    @Primary
    public RoutingDataSource mainDataSource() {

        var dsMainb = DataSourceBuilder.create()
                .url(url)
                .username(getWriteUsername())
                .password(password);

        if (url.startsWith("jdbc:aws-wrapper:mysql:")) {
            dsMainb.driverClassName("software.amazon.jdbc.Driver");
//            exceptionOverrideClassname = HikariCPSQLException.class.getCanonicalName();
        }
        var dsMain = dsMainb.build();


        var hConfig = ((HikariConfig) dsMain);
        hConfig.setPoolName("mainPool");
        hConfig.setAutoCommit(hAutoCommit);
        hConfig.setMinimumIdle(hMinIdle.intValue());
        hConfig.setLeakDetectionThreshold(hLeakTreshold);
        hConfig.setMaximumPoolSize(hMaxPoolSize.intValue());
        hConfig.setConnectionTimeout(hConnTimeout);
        hConfig.setIdleTimeout(hIdleTimeout);
        hConfig.setMaxLifetime(hMaxLifetime);
        hConfig.setTransactionIsolation(null);

        if (StringUtils.isNotEmpty(exceptionOverrideClassname)) {
            hConfig.setExceptionOverrideClassName(exceptionOverrideClassname);
        }
        addCommonDSProperties(hConfig);

        log.info("Main Hikari Pool set to {}/{} minIdle/maxConn", hMinIdle, hMaxPoolSize);

        var rUrl = readUrl;
        if (StringUtils.isEmpty(rUrl)) {
            rUrl = url;
        }
        var dsReplicab = DataSourceBuilder.create()
                .url(rUrl)
                .username(getReadUsername())
                .password(password);


        if (url.startsWith("jdbc:aws-wrapper:mysql:")) {
            dsReplicab.driverClassName("software.amazon.jdbc.Driver");
//            exceptionOverrideClassname = HikariCPSQLException.class.getCanonicalName();
        }

        var dsReplica = dsReplicab.build();

        hConfig = ((HikariConfig) dsReplica);
        hConfig.setPoolName("replicaPool");
        hConfig.setAutoCommit(true);
        hConfig.setMinimumIdle(hMinIdle.intValue());
        hConfig.setLeakDetectionThreshold(hLeakTreshold);
        hConfig.setMaximumPoolSize(hMaxPoolSize.intValue());
        hConfig.setConnectionTimeout(hConnTimeout);
        hConfig.setIdleTimeout(hIdleTimeout);
        hConfig.setMaxLifetime(hMaxLifetime);
        hConfig.setReadOnly(true);
        hConfig.setTransactionIsolation("TRANSACTION_READ_COMMITTED");
        if (StringUtils.isNotEmpty(exceptionOverrideClassname)) {
            hConfig.setExceptionOverrideClassName(exceptionOverrideClassname);
        }

        addCommonDSProperties(hConfig);


        Map<DbType, DataSource> dsMap = new EnumMap<>(DbType.class);
        dsMap.put(DbType.MASTER, dsMain);
        dsMap.put(DbType.REPLICA, dsReplica);

        return new RoutingDataSource(dsMap, dsMain);

    }

    private String getLiquibaseUsername() {
        if (username.endsWith("_*")) {
            return username.replace("_*", "_admin");
        }
        return lb_username;
    }

    private String getWriteUsername() {
        if (username.endsWith("_*")) {
            return username.replace("_*", "_write");
        }
        return username;
    }

    private String getReadUsername() {
        if (username.endsWith("_*")) {
            return username.replace("_*", "_read");
        }
        return ("test".equalsIgnoreCase(password)) ? username : "read";
    }


    private static void addCommonDSProperties(HikariConfig hConfig) {
        hConfig.addDataSourceProperty("useServerPrepStmts", "false");
        hConfig.addDataSourceProperty("cachePrepStmts", "true");
        hConfig.addDataSourceProperty("prepStmtCacheSize", "1500");
        hConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "6000");
        hConfig.addDataSourceProperty("useLocalSessionState", "true");
        hConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hConfig.addDataSourceProperty("maintainTimeStats", "false");
        hConfig.addDataSourceProperty("useLocalTransactionState", "true");
        hConfig.addDataSourceProperty("useCompression", "true");
    }


    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(JpaProperties jpaProps) {
        var entityManagerFactoryBean = new LocalContainerEntityManagerFactoryBean();
        entityManagerFactoryBean.setPersistenceUnitName(getClass().getSimpleName());
        entityManagerFactoryBean.setPersistenceProvider(new HibernatePersistenceProvider());
        entityManagerFactoryBean.setDataSource(mainDataSource());
        entityManagerFactoryBean.setPackagesToScan("com.ously.gamble");

        var vendorAdapter = new HibernateJpaVendorAdapter();
        var jpaDialect = vendorAdapter.getJpaDialect();
        jpaDialect.setPrepareConnection(false);
        entityManagerFactoryBean.setJpaVendorAdapter(vendorAdapter);
        entityManagerFactoryBean.setJpaProperties(additionalProperties(jpaProps));
        return entityManagerFactoryBean;
    }


    protected static Properties additionalProperties(JpaProperties jpaProps) {
        var properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        properties.setProperty("hibernate.hbm2ddl.auto", "none");
        properties.setProperty("hibernate.connection.provider_disables_autocommit", "true");
        properties.setProperty("hibernate.query.plan_cache_max_size", "4096");
        properties.setProperty("hibernate.query.plan_parameter_metadata_max_size", "4096");
        properties.putAll(jpaProps.getProperties());
        return properties;
    }


}
