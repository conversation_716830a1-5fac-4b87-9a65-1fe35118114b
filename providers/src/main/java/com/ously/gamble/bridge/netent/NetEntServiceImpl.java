package com.ously.gamble.bridge.netent;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.netent.payload.NEBalanceResponse;
import com.ously.gamble.bridge.netent.payload.NEGameRoundRequest;
import com.ously.gamble.bridge.netent.payload.NEGameRoundResponse;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import freemarker.template.Configuration;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.persistence.OptimisticLockException;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;


@Service
@ConditionalOnBean(NetentConfiguration.class)
public class NetEntServiceImpl extends BridgeBaseV2 implements NetEntService {

    /**
     * Maximum token validity. Since this token is only used for the launch it should be rather small
     */
    private static final long JWT_TOKEN_VALIDITY_IN_MINUTES = 60 * 10;

    final Logger log = LoggerFactory.getLogger(NetEntServiceImpl.class);

    @Autowired
    private Configuration freemarkerConfig;

    @Autowired
    NetentConfiguration config;

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {
        log.debug("CreateGame  for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);
        var regID = "NT-" + user.getId() + '-' + config.getCasinoName();
        // Token is combination of playerId

        // for netent we need to create a html page via templating.
        // parameters are gameid and the playerId and the platform (mobile/web)
        var jwt = createJWT(regID, game.getGameId(), config.getJwtSecret(),
                config.getActiveCurrency(), settings.getBetLevel());
        var gameHtml = createHTML(game.getGameId(), regID, jwt, config.getGameServerUrl(), platform);
//        log.info("Created jwt:{}", jwt);
        var gameInstance = new GameInstance();
        gameInstance.setBetLevel(settings.getBetLevel());
        gameInstance.setGameHtml(gameHtml);
        gameInstance.setGameUrl("");
        gameInstance.setToken(regID);
        gameInstance.setAuthToken(regID);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    /**
     * Create the HTML code used to wrap a game-launch
     *
     * @param gameId   the gameID
     * @param playerId the custom playerId
     * @param jwt      the token
     * @return html code for iframe or direct integration
     */
    protected String createHTML(String gameId, String playerId, String jwt, String gameServerUrl,
                                GamePlatform gp) throws Exception {

        switch (gp) {
            case IOS, ANDROID -> {
                var t = freemarkerConfig.getTemplate("netent_template_mobile.ftl");
                Map<Object, Object> props = new HashMap<>(5);
                props.put("gameId", gameId);
                props.put("playerId", playerId);
                props.put("jwt", jwt);
                props.put("gameServerUrl", gameServerUrl);
                return FreeMarkerTemplateUtils.processTemplateIntoString(t, props);
            }
            default -> {
                var t = freemarkerConfig.getTemplate("netent_template_web.ftl");
                Map<Object, Object> props = new HashMap<>(5);
                props.put("gameId", gameId);
                props.put("playerId", playerId);
                props.put("jwt", jwt);
                props.put("gameServerUrl", gameServerUrl);
                return FreeMarkerTemplateUtils.processTemplateIntoString(t, props);
            }
        }
    }

    @Override
    @Transactional
    public NEBalanceResponse getBalance(String playerId) {
        var eResp = new NEBalanceResponse();

        var token = getToken(playerId);
        if (token == null) {
            eResp.setErrorCode(0);
            eResp.setErrorMessage("cannot find a session for given playerId");
            return eResp;
        }

        var wallet = getWallet(token.getUserId());
        if (wallet != null) {
            eResp.setBalance(wallet.getBalance().setScale(2, RoundingMode.DOWN));
            eResp.setCurrency(config.getActiveCurrency());
        } else {
            eResp.setErrorCode(0);
            eResp.setErrorMessage("cannot find a wallet for given playerId");
        }
        return eResp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public NEGameRoundResponse doGameRound(NEGameRoundRequest req) {
        var gResp = new NEGameRoundResponse();

        var token = getToken(req.getPlayerId());
        if (token == null) {
            gResp.setErrorCode(0);
            gResp.setErrorMessage("cannot find a session for given playerId");
            return gResp;
        }


        // check if tx already exists
        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.OPENCLOSE);
        var winAmount = (req.getWinAmount() == null) ? BigDecimal.ZERO : req.getWinAmount();
        var betAmount = (req.getBetAmount() == null) ? BigDecimal.ZERO : req.getBetAmount();
        txReq.setWin(winAmount);
        txReq.setBet(betAmount);

        if (betAmount.doubleValue() > 0 && winAmount.doubleValue() == 0) {
            txReq.setType(TransactionType.BET);
        } else if (betAmount.doubleValue() == 0 && winAmount.doubleValue() > 0) {
            txReq.setType(TransactionType.WIN);
        } else {
            txReq.setType(TransactionType.DIRECTWIN);
        }
        txReq.setExternalOrigId(txReq.getType().name() + ':' + req.getGameRoundId());
        txReq.setRoundRef(req.getGameRoundId().toString());
        try {
            var txResponse = addTxFromProvider(txReq);
            gResp.setBalance(txResponse.getNewBalance());
            gResp.setCurrency(config.getActiveCurrency());
        } catch (OuslyTransactionException e) {
            log.error("Error performing gameround while trying to add tx", e);
            gResp.setErrorCode(2);
            gResp.setErrorMessage("Insuff. funds");
        }
        return gResp;
    }

    @SuppressWarnings("UseOfObsoleteDateTimeApi")
    protected static String createJWT(String playerId, String gameId, String secret,
                                      String currency,
                                      int betLevel) {

        Claims clms = null;
        Map<String, Object> claims = new HashMap<>(8);

        claims.put("pip", "netent");
        claims.put("gid", gameId);
        claims.put("lvl", 1);
        claims.put("currency", currency);
        // Custom betlevels, evtl. mult by N for VIP/HigherLevels/Highroller
        claims.put("cfg", createCustomBetlevels(betLevel));
        claims.put("betLevels", "1");

//        var secretKey = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));

        return Jwts.builder().setClaims(claims).
                setAudience("netent").
                setSubject(playerId).
                setIssuedAt(new Date()).
                setExpiration(new Date(System.currentTimeMillis() + JWT_TOKEN_VALIDITY_IN_MINUTES * 60 * 1000))
                .signWith(SignatureAlgorithm.HS512, secret.getBytes(StandardCharsets.UTF_8)).compact();
    }

    private static Map<String, Object> createCustomBetlevels(int betlevel) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("betLevels", 1);
        if (betlevel == 1) {
            map.put("defaultBet", 0.5f);
            map.put("bets", new float[]{0.5f, 1.0f, 2.0f,
                    5.0f,
                    10.0f, 15.0f, 25.0f, 50.0f, 75.0f, 100.0f});
        } else if (betlevel == 2) {
            map.put("defaultBet", 10.0f);
            map.put("bets", new float[]{
                    10.0f, 15.0f, 25.0f, 50.0f, 75.0f, 100.0f, 250.0f, 500.0f, 750.0f, 1000.0f});
        } else if (betlevel == 3) {
            map.put("defaultBet", 250.0f);
            map.put("bets", new float[]{
                    100.0f,250.0f, 500.0f, 1000.0f, 1500.0f, 2500.0f, 4000.0f, 5000.0f});
        } else {
            map.put("defaultBet", 0.01f);
            map.put("bets", new float[]{0.01f, 0.02f, 0.04f, 0.06f, 0.10f, 0.15f, 0.3f, 0.5f, 1.0f, 2.0f,
                    5.0f,
                    10.0f});
        }
        return map;
    }

}
