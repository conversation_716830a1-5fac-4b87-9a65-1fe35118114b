package com.ously.gamble.bridge.gamebeat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.gamebeat.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.SessionTransaction;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;

@Service
@ConditionalOnBean(GamebeatConfiguration.class)
public class GamebeatServiceImpl extends BridgeBaseV2 implements GamebeatService {

    private static final BigDecimal BD100 = BigDecimal.valueOf(100L);
    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private RestTemplate gamebeatRestTemplate;

    @Autowired
    ObjectMapper om;

    @Autowired
    GamebeatConfiguration config;

    private Mac sha256MAC;

    @PostConstruct
    public void initialize() {
        sha256MAC = HmacUtils.getInitializedMac("HmacSHA256", config.getSecret().getBytes(StandardCharsets.UTF_8));
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public GBPlayResponse play(GBPlayRequest request) throws GamebeatError {

        log.debug("GB-PLAY-REQ:{}", request);
        var token = getToken(request.getToken());
        if (token == null) {
            log.error("GB-balance-error: invalid token");
            throw new GamebeatError(404, "invalid token");
        }

        if (request.getActions() == null || request.getActions().isEmpty()) {
            // Balance request
            var w = getWallet(token.getWalletId());
            long balance = w.getBalance().multiply(BD100).longValue();
            var gmmResponse = new GBPlayResponse(balance);
            log.debug("GB-BALANCE-RESP:{}", gmmResponse);
            return gmmResponse;
        }

        List<GBActionResponse> doneActions = new ArrayList<>();
        long currentBalance = 0;
        // Perform transactions
        for (var action : request.getActions()) {
            if (action.getAction().equalsIgnoreCase("bet")) {
                BigDecimal betAmount = new BigDecimal(action.getAmount()).divide(BD100, 2, RoundingMode.HALF_UP);

                var txr = new TxRequest(token);
                txr.setType(TransactionType.BET);
                txr.setExternalOrigId(action.getActionId());
                txr.setExternal_tx_ref(action.getActionId() + ":BET:" + request.getGameId());
                txr.setRoundRef(request.getGameId());
                txr.setBet(betAmount);
                txr.setWin(BigDecimal.ZERO);
                txr.setVendorName(config.getVendorName());
                TxResponse txResponse;
                try {
                    txResponse = addTxFromProvider(txr);
                } catch (OuslyTransactionException e) {
                    if (e instanceof OuslyOutOfMoneyException) {
                        log.debug("Error while trying to add debit Tx: {}", request, e);
                        throw new GamebeatError(449L, "not enough funds", ((OuslyOutOfMoneyException) e).getBalance().multiply(BD100).longValue());
                    }
                    log.error("Error on Gamebeat-Bet:", e);
                    throw new GamebeatError(500L, "unknownError");
                }
                currentBalance = txResponse.getNewBalance().multiply(BD100).longValue();
                doneActions.add(new GBActionResponse(null, action.getActionId(), null));
            } else {
                BigDecimal winAmount = new BigDecimal(action.getAmount()).divide(BD100, 2, RoundingMode.HALF_UP);

                var txr = new TxRequest(token);
                txr.setType(TransactionType.WIN);
                txr.setExternalOrigId(action.getActionId());
                txr.setExternal_tx_ref(action.getActionId() + ":WIN:" + request.getGameId());
                txr.setRoundRef(request.getGameId());
                txr.setBet(BigDecimal.ZERO);
                txr.setWin(winAmount);
                txr.setVendorName(config.getVendorName());
                TxResponse txResponse;
                try {
                    txResponse = addTxFromProvider(txr);
                } catch (Exception e) {
                    log.error("Error on Gamebeat-Win:", e);
                    throw new GamebeatError(500L, "unknownError");
                }
                currentBalance = txResponse.getNewBalance().multiply(BD100).longValue();
                doneActions.add(new GBActionResponse(null, action.getActionId(), null));
            }
        }

        // Now return compiled response
        GBPlayResponse response = new GBPlayResponse(currentBalance);
        response.setTransactions(doneActions);
        response.setGameId(request.getGameId());
        return response;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public GBRollbackResponse rollback(GBRollbackRequest request) throws GamebeatError {
        log.debug("GB-ROLLBACK-REQ:{}", request);
        var token = getToken(request.getToken());
        if (token == null) {
            //TODO: Evtl. try to get session even if token is invalid (player has created another session)
            log.error("GB-rollback-error: invalid token");
            throw new GamebeatError(404, "invalid token");
        }

        if (request.getActions() == null || request.getActions().isEmpty()) {
            // Balance request / also valid response to a rollback
            var w = getWallet(token.getWalletId());
            long balance = w.getBalance().multiply(BD100).longValue();
            var gmmResponse = new GBRollbackResponse(balance);
            log.debug("GB-BALANCE-RESP:{}", gmmResponse);
            return gmmResponse;
        }

        List<GBActionResponse> doneActions = new ArrayList<>();

        var wallet = getWallet(token.getWalletId());
        var balance = wallet.getBalance();
        // Perform transactions
        for (var action : request.getActions()) {
            if (action.getAction().equalsIgnoreCase("rollback")) {

                // create old exOrigId
                String oldExtOrigId = action.getOriginalActionId();

                SessionTransaction byUserIdAndVendorAndOrigId = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), oldExtOrigId);

                // if not found or already cancelled then just return
                if (byUserIdAndVendorAndOrigId == null || byUserIdAndVendorAndOrigId.isCancelled()) {
                    doneActions.add(new GBActionResponse(null, action.getActionId(), null));
                    continue;
                }
                wallet = getWallet(token.getWalletId());
                TxResponse rollbackResponse = performRollback(byUserIdAndVendorAndOrigId, wallet);
                balance = rollbackResponse.getNewBalance();
                doneActions.add(new GBActionResponse(null, action.getActionId(), null));
            }
        }

        // Now return compiled response
        GBRollbackResponse response = new GBRollbackResponse(balance.multiply(BD100).longValue());
        response.setGameId(request.getGameId());
        response.setTransactions(doneActions);
        return response;
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform, GameSettings settings) {
        log.info("CreateGame (GAMOMAT) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            try {
                GBCreateSessionRequest gbCreateSessionRequest = new GBCreateSessionRequest();
                gbCreateSessionRequest.setCasinoId(config.getCasinoId());
                gbCreateSessionRequest.setGame(game.getGameId());
                gbCreateSessionRequest.setIp(settings.getIp());
                gbCreateSessionRequest.setUrls(new GBUrl("https://spinarena.net"));
                gbCreateSessionRequest.setUser(createGBUser(user, settings.getBetLevel()));
                gbCreateSessionRequest.setClientType(getClientType(platform));
                gbCreateSessionRequest.setLocale(Objects.requireNonNull(user.getLangCode(), "EN").toLowerCase(Locale.ROOT));
                gbCreateSessionRequest.setCurrency(selectCurrencyForBetlevel(settings.getBetLevel()));
                gbCreateSessionRequest.setSessionToken(token);

                // create body String
                String body = om.writeValueAsString(gbCreateSessionRequest);
                String signature = getSignature(body);

                var headers = new HttpHeaders();
                headers.add("X-REQUEST-SIGN", signature);
                headers.add("Content-Type", "application/json");
                log.debug("Sending Session Create {}/{}", body, headers);

                var entity = new HttpEntity<>(body, headers);
                var respEnt = gamebeatRestTemplate.postForEntity(config.getApiUrl() + "/api/sessions/real", entity, GBCreateSessionResponse.class);
                log.debug("Got {}", respEnt);
                gameUrl = respEnt.getBody().getLaunchOptions().getGameUrl();
            } catch (Exception e) {
                log.error("Error creating session for gamebeat", e);
            }

        }

        var
                gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private String selectCurrencyForBetlevel(int betLevel) {
        return switch (betLevel) {
            case 1 -> "AC1";
            case 2 -> "AC2";
            case 3 -> "AC3";
            default -> config.currency;
        };
    }

    private String getClientType(GamePlatform platform) {
        return switch (platform) {
            case IOS, ANDROID -> "mobile";
            default -> "desktop";
        };
    }

    /**
     * @param user     the user
     * @param betlevel betlevel for session
     * @return GBUser with minimal props
     */
    private GBUser createGBUser(CasinoUser user, int betlevel) {
        GBUser gbUser = new GBUser();
        gbUser.setUserId("GB-" + user.getId() + "-" + betlevel);
        return gbUser;
    }

    @Override
    public String getSignature(String body) {
        return Hex.encodeHexString(sha256MAC.doFinal(body.getBytes(StandardCharsets.UTF_8)), true);
    }

}
