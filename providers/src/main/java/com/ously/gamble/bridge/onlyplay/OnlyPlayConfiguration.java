package com.ously.gamble.bridge.onlyplay;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import com.ously.gamble.conditions.ConditionalOnSocial;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "onlyplay")
@ConditionalOnProperty(
        value = "onlyplay.enabled",
        havingValue = "true",
        matchIfMissing = true)
@ConditionalOnSocial
public class OnlyPlayConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "ONLYPLAY";
    public static final String BRIDGE_ONLYPLAY = "/bridge/onlyplay";


    int partnerId = 762;
    String secret = "R6x8ZDZNMTtVOpysI8EWKEqOkExr0fCGYUdPsFQEQ7mFtyQZhXzAS";
    String launchUrl = "https://int.stage.onlyplay.net/api";
    String currency = "USD";


    public int getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getLaunchUrl() {
        return launchUrl;
    }

    public void setLaunchUrl(String launchUrl) {
        this.launchUrl = launchUrl;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }


    @Override
    public String getBridgeName() {
        return BRIDGE_ONLYPLAY;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }


}
