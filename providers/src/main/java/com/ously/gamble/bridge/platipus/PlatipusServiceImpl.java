package com.ously.gamble.bridge.platipus;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.platipus.payload.PTBalanceResponse;
import com.ously.gamble.bridge.platipus.payload.PTTrResponse;
import com.ously.gamble.bridge.platipus.payload.PTUsernameResponse;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.ously.gamble.persistence.model.TransactionType.BET;
import static com.ously.gamble.persistence.model.TransactionType.WIN;

@Service
@ConditionalOnBean(PlatipusConfiguration.class)
public class PlatipusServiceImpl extends BridgeBaseV2 implements PlatipusService {

    final Logger log = LoggerFactory.getLogger(PlatipusServiceImpl.class);

    @Autowired
    PlatipusConfiguration config;

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame (PLATIPUS) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(config.getLauncherurl());
            bld.addParameter("key", config.getApiKey());
            bld.addParameter("userid", user.getId().toString());
            bld.addParameter("token", token);
            bld.addParameter("gameconfig", game.getGameId());
            bld.addParameter("lang", settings.getLanguage());
            gameUrl = bld.build().toString();
        }

        var gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    @Override
    @Transactional
    public PTBalanceResponse balance(String tokenStr, Long providerId, Long userId, String md5) {
        var token = getToken(tokenStr);
        if (token == null) {
            return new PTBalanceResponse(CODE_USER_NOT_FOUND);
        }
        if (checkmd5(providerId.toString() + config.getSecret() + userId.toString(), md5)) {
            return new PTBalanceResponse(CODE_INVALID_HASH);
        }

        var w = getWallet(token.getUserId());
        return new PTBalanceResponse(token.getUserId(), w.getBalance(), config.getCurrency());
    }

    private boolean checkmd5(String s, String md5) {
        log.debug("md5 of '{}'", s);
        return DigestUtils.md5Hex(s).compareToIgnoreCase(md5) != 0;
    }

    @Override
    @Transactional
    public PTBalanceResponse balance2(String tokenStr, Long providerId, Long userId, String hash) {
        var token = getToken(tokenStr);
        if (token == null) {
            return new PTBalanceResponse(CODE_USER_NOT_FOUND);
        }
        if (checksha256(providerId.toString() + config.getSecret() + userId.toString(), hash)) {
            return new PTBalanceResponse(CODE_INVALID_HASH);
        }
        var w = getWallet(token.getUserId());
        return new PTBalanceResponse(token.getUserId(), w.getBalance(), config.getCurrency());
    }

    private boolean checksha256(String s, String hash) {
        log.debug("sha256 of '{}'", s);
        return DigestUtils.sha256Hex(s).compareToIgnoreCase(hash) != 0;
    }

    @Override
    @Transactional
    public PTTrResponse betWin(String tokenStr, Long providerId, Long userId, String md5,
                               BigDecimal amount, String remoteTransId, Long gameId,
                               String gameName, Long roundId, String trType, String finished) {
        var token = getToken(tokenStr);
        var txType = ("BET".equalsIgnoreCase(trType)) ? BET : WIN;
        if (token == null) {
            if (txType == WIN) {
                var sessionByToken = findSessionByToken(tokenStr);
                if (sessionByToken.isPresent()) {
                    token = new SessionCacheEntry(sessionByToken.get(), null, null);
                } else {
                    return new PTTrResponse(CODE_USER_NOT_FOUND);
                }
            } else {
                return new PTTrResponse(CODE_USER_NOT_FOUND);
            }
        }
        if (checkmd5(providerId.toString() + config.getSecret() + userId.toString() + getSignAmount(amount), md5)) {
            return new PTTrResponse(CODE_INVALID_HASH);
        }

        // now check tx params
        if (txType == BET) {
            if (amount.signum() > 0) {
                return new PTTrResponse(CODE_INVALID_AMOUNT);
            }
        } else {
            if (amount.signum() < 0) {
                return new PTTrResponse(CODE_INVALID_AMOUNT);
            }
            if (amount.signum() == 0) {
                var w = getWallet(token.getUserId());
                return new PTTrResponse(0, w.getBalance());
            }
        }

        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setRoundRef(roundId.toString());
        txReq.setType(txType);
        if (txType == BET) {
            txReq.setBet(amount.abs());
            txReq.setWin(BigDecimal.ZERO);
        } else {
            txReq.setWin(amount.abs());
            txReq.setBet(BigDecimal.ZERO);
        }
        txReq.setExternalOrigId(remoteTransId);
        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (Exception e) {
            if (e instanceof OuslyOutOfMoneyException) {
                return new PTTrResponse(CODE_INSUFFICIENT_BALANCE);
            }
            return new PTTrResponse(CODE_INTERNAL_SYSTEM_ERROR);
        }
        return new PTTrResponse(0, txResp.getNewBalance());
    }

    private static String getSignAmount(BigDecimal amount) {
        return String.format("%.2f", amount);
    }

    @Override
    @Transactional
    public PTUsernameResponse getUserName(String tokenStr, Long providerId, Long userId,
                                          String hash) {
        var token = getToken(tokenStr);
        if (token == null) {
            return new PTUsernameResponse(CODE_USER_NOT_FOUND);
        }
        if (checksha256(providerId.toString() + config.getSecret() + userId.toString(), hash)) {
            return new PTUsernameResponse(CODE_INVALID_HASH);
        }
        var u = getCasinoUser(token.getUserId());
        return new PTUsernameResponse(token.getUserId(), u.getDisplayName());
    }

    @Override
    @Transactional
    public PTTrResponse refund(String tokenStr, Long providerId, Long userId, String md5,
                               BigDecimal amount, String remoteTranId, Long gameId, String gameName,
                               Long roundId, String trType) {
        var token = getToken(tokenStr);
        if (token == null) {
            var sessionByToken = findSessionByToken(tokenStr);
            if (sessionByToken.isEmpty()) {
                return new PTTrResponse(CODE_USER_NOT_FOUND);
            }
            token = new SessionCacheEntry(sessionByToken.get(), null, null);
        }
        if (checkmd5(providerId.toString() + config.getSecret() + userId.toString() + getSignAmount(amount), md5)) {
            return new PTTrResponse(CODE_INVALID_HASH);
        }

        var oldTx = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), remoteTranId);
        var w = getWallet(token.getUserId());
        if (oldTx == null || oldTx.isCancelled()) {
            return new PTTrResponse(0, w.getBalance());
        }

        try {
            var txResponse = performRollback(oldTx, w);
            return new PTTrResponse(0, txResponse.getNewBalance());
        } catch (Exception e) {
            log.warn("PT Refund error", e);
            return new PTTrResponse(CODE_INTERNAL_SYSTEM_ERROR);
        }
    }
}
