package com.ously.gamble.bridge.gamzix;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.gamzix.payload.GZStatus;
import com.ously.gamble.bridge.gamzix.payload.GZStatusRequest;
import com.ously.gamble.bridge.gamzix.payload.GZTransactionRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(GamzixConfiguration.BRIDGE_GAMZIX)
@ConditionalOnBean(GamzixConfiguration.class)
public class GamzixController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    GamzixConfiguration config;

    @Autowired
    ObjectMapper om;

    @Autowired
    GamzixService service;

    @GetMapping("/balance")
    @ResponseBody
    public ResponseEntity<GZStatus> getBalance(@RequestParam(name = "pid") String pid) {
        var balance = service.balance(pid);
        return ResponseEntity.ok(balance);
    }

    @PostMapping("/withdraw")
    @ResponseBody
    public ResponseEntity<GZStatus> withdraw(@RequestBody String req, @RequestHeader MultiValueMap<String, String> headers) {

        if (checkAuthSig(req, headers.getFirst("x-signature"))) {
            return ResponseEntity.ok(new GZStatus(GamzixService.GZ_INVALIDSIG));
        }
        try {
            var gzReq = om.readValue(req, GZTransactionRequest.class);
            return ResponseEntity.ok(service.withdraw(gzReq));
        } catch (JsonProcessingException e) {
            return ResponseEntity.ok(createErrorOnFailingDeser(req));
        }

    }

    @PostMapping("/deposit")
    @ResponseBody
    public ResponseEntity<GZStatus> deposit(@RequestBody String req, @RequestHeader MultiValueMap<String, String> headers) {
        if (checkAuthSig(req, headers.getFirst("x-signature"))) {
            return ResponseEntity.ok(new GZStatus(GamzixService.GZ_INVALIDSIG));
        }
        try {
            var gzReq = om.readValue(req, GZTransactionRequest.class);
            return ResponseEntity.ok(service.deposit(gzReq));
        } catch (JsonProcessingException e) {
            return ResponseEntity.ok(createErrorOnFailingDeser(req));
        }
    }

    @PostMapping("/cancel")
    @ResponseBody
    public ResponseEntity<GZStatus> cancel(@RequestBody String req, @RequestHeader MultiValueMap<String, String> headers) {
        if (checkAuthSig(req, headers.getFirst("x-signature"))) {
            return ResponseEntity.ok(new GZStatus(GamzixService.GZ_INVALIDSIG));
        }
        try {
            var gzReq = om.readValue(req, GZTransactionRequest.class);
            return ResponseEntity.ok(service.cancel(gzReq));
        } catch (JsonProcessingException e) {
            return ResponseEntity.ok(createErrorOnFailingDeser(req));
        }
    }

    @PostMapping("/opened")
    @ResponseBody
    public ResponseEntity<GZStatus> opened(@RequestBody String req, @RequestHeader MultiValueMap<String, String> headers) {
        if (checkAuthSig(req, headers.getFirst("x-signature"))) {
            return ResponseEntity.ok(new GZStatus(GamzixService.GZ_INVALIDSIG));
        }
        try {
            var gzReq = om.readValue(req, GZStatusRequest.class);
            return ResponseEntity.ok(service.opened(gzReq));
        } catch (JsonProcessingException e) {
            return ResponseEntity.ok(createErrorOnFailingDeser(req));
        }
    }

    @PostMapping("/closed")
    @ResponseBody
    public ResponseEntity<GZStatus> closed(@RequestBody String req, @RequestHeader MultiValueMap<String, String> headers) {
        if (checkAuthSig(req, headers.getFirst("x-signature"))) {
            return ResponseEntity.ok(new GZStatus(GamzixService.GZ_INVALIDSIG));
        }
        try {
            var gzReq = om.readValue(req, GZStatusRequest.class);
            return ResponseEntity.ok(service.closed(gzReq));
        } catch (JsonProcessingException e) {
            return ResponseEntity.ok(createErrorOnFailingDeser(req));
        }
    }

    private static GZStatus createErrorOnFailingDeser(String req) {
        var status = new GZStatus(GamzixService.GZ_UNEXPECTED);
        status.setError("Error parsing request");
        return status;
    }

    private boolean checkAuthSig(String req, String first) {
        if (first == null) {
            return false;
        }
        var authCorrect = first.equalsIgnoreCase(DigestUtils.sha512Hex(req + ':' + config.getSecret()));
        if (!authCorrect) {
            log.warn("GZ:Invalid Sig for {}:{}", req, first);
        }
        return !authCorrect;
    }

}
